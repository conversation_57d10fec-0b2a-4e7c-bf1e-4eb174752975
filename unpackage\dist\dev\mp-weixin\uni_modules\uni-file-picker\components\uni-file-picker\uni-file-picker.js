(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker"],{282:function(e,t,i){"use strict";i.r(t);var n=i(283),r=i(285);for(var s in r)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return r[e]}))}(s);i(294);var u,a=i(32),l=Object(a["default"])(r["default"],n["render"],n["staticRenderFns"],!1,null,null,null,!1,n["components"],u);l.options.__file="uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker.vue",t["default"]=l.exports},283:function(e,t,i){"use strict";i.r(t);var n=i(284);i.d(t,"render",(function(){return n["render"]})),i.d(t,"staticRenderFns",(function(){return n["staticRenderFns"]})),i.d(t,"recyclableRender",(function(){return n["recyclableRender"]})),i.d(t,"components",(function(){return n["components"]}))},284:function(e,t,i){"use strict";var n;i.r(t),i.d(t,"render",(function(){return r})),i.d(t,"staticRenderFns",(function(){return u})),i.d(t,"recyclableRender",(function(){return s})),i.d(t,"components",(function(){return n}));var r=function(){var e=this,t=e.$createElement,i=(e._self._c,e.title?e.filesList.length:null);e.$mp.data=Object.assign({},{$root:{g0:i}})},s=!1,u=[];r._withStripped=!0},285:function(e,t,i){"use strict";i.r(t);var n=i(286),r=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);t["default"]=r.a},286:function(e,t,i){"use strict";(function(e,n){var r=i(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var s=r(i(37)),u=r(i(18)),a=r(i(11)),l=r(i(39)),o=i(292),c=i(293);function f(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function d(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?f(Object(i),!0).forEach((function(t){(0,a.default)(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):f(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}var p=function(){i.e("uni_modules/uni-file-picker/components/uni-file-picker/upload-image").then(function(){return resolve(i(390))}.bind(null,i)).catch(i.oe)},h=function(){i.e("uni_modules/uni-file-picker/components/uni-file-picker/upload-file").then(function(){return resolve(i(397))}.bind(null,i)).catch(i.oe)},m={name:"uniFilePicker",components:{uploadImage:p,uploadFile:h},options:{virtualHost:!0},emits:["select","success","fail","progress","delete","update:modelValue","input"],props:{modelValue:{type:[Array,Object],default:function(){return[]}},value:{type:[Array,Object],default:function(){return[]}},disabled:{type:Boolean,default:!1},disablePreview:{type:Boolean,default:!1},delIcon:{type:Boolean,default:!0},autoUpload:{type:Boolean,default:!0},limit:{type:[Number,String],default:9},mode:{type:String,default:"grid"},fileMediatype:{type:String,default:"image"},fileExtname:{type:[Array,String],default:function(){return[]}},title:{type:String,default:""},listStyles:{type:Object,default:function(){return{border:!0,dividline:!0,borderStyle:{}}}},imageStyles:{type:Object,default:function(){return{width:"auto",height:"auto"}}},readonly:{type:Boolean,default:!1},returnType:{type:String,default:"array"},sizeType:{type:Array,default:function(){return["original","compressed"]}},sourceType:{type:Array,default:function(){return["album","camera"]}},provider:{type:String,default:""}},data:function(){return{files:[],localValue:[]}},watch:{value:{handler:function(e,t){this.setValue(e,t)},immediate:!0},modelValue:{handler:function(e,t){this.setValue(e,t)},immediate:!0}},computed:{filesList:function(){var e=[];return this.files.forEach((function(t){e.push(t)})),e},showType:function(){return"image"===this.fileMediatype?this.mode:"list"},limitLength:function(){return"object"===this.returnType?1:this.limit?this.limit>=9?9:this.limit:1}},created:function(){e.config&&e.config.provider||(this.noSpace=!0,e.chooseAndUploadFile=o.chooseAndUploadFile),this.form=this.getForm("uniForms"),this.formItem=this.getForm("uniFormsItem"),this.form&&this.formItem&&this.formItem.name&&(this.rename=this.formItem.name,this.form.inputChildrens.push(this))},methods:{clearFiles:function(e){var t=this;0===e||e?this.files.splice(e,1):(this.files=[],this.$nextTick((function(){t.setEmit()}))),this.$nextTick((function(){t.setEmit()}))},upload:function(){var e=[];return this.files.forEach((function(t,i){"ready"!==t.status&&"error"!==t.status||e.push(Object.assign({},t))})),this.uploadFiles(e)},setValue:function(e,t){var i=this;return(0,l.default)(s.default.mark((function t(){var n,r,u,a;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=function(){var e=(0,l.default)(s.default.mark((function e(t){var n,r;return s.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(n=/cloud:\/\/([\w.]+\/?)\S*/,r="",r=t.fileID?t.fileID:t.url,!n.test(r)){e.next=8;break}return t.fileID=r,e.next=7,i.getTempFileURL(r);case 7:t.url=e.sent;case 8:return t.url&&(t.path=t.url),e.abrupt("return",t);case 10:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),"object"!==i.returnType){t.next=10;break}if(!e){t.next=7;break}return t.next=5,n(e);case 5:t.next=8;break;case 7:e={};case 8:t.next=19;break;case 10:e||(e=[]),r=0;case 12:if(!(r<e.length)){t.next=19;break}return u=e[r],t.next=16,n(u);case 16:r++,t.next=12;break;case 19:i.localValue=e,i.form&&i.formItem&&!i.is_reset&&(i.is_reset=!1,i.formItem.setValue(i.localValue)),a=Object.keys(e).length>0?e:[],i.files=[].concat(a);case 23:case"end":return t.stop()}}),t)})))()},choose:function(){this.disabled||(this.files.length>=Number(this.limitLength)&&"grid"!==this.showType&&"array"===this.returnType?n.showToast({title:"您最多选择 ".concat(this.limitLength," 个文件"),icon:"none"}):this.chooseFiles())},chooseFiles:function(){var t=this,i=(0,c.get_extname)(this.fileExtname);e.chooseAndUploadFile({type:this.fileMediatype,compressed:!1,sizeType:this.sizeType,sourceType:this.sourceType,extension:i.length>0?i:void 0,count:this.limitLength-this.files.length,onChooseFile:this.chooseFileCallback,onUploadProgress:function(e){t.setProgress(e,e.index)}}).then((function(e){t.setSuccessAndError(e.tempFiles)})).catch((function(e){console.log("选择失败",e)}))},chooseFileCallback:function(e){var t=this;return(0,l.default)(s.default.mark((function i(){var n,r,u,a,l,o,f,p;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:n=(0,c.get_extname)(t.fileExtname),r=1===Number(t.limitLength)&&t.disablePreview&&!t.disabled||"object"===t.returnType,r&&(t.files=[]),u=(0,c.get_files_and_is_max)(e,n),a=u.filePaths,l=u.files,n&&n.length>0||(a=e.tempFilePaths,l=e.tempFiles),o=[],f=0;case 7:if(!(f<l.length)){i.next=21;break}if(!(t.limitLength-t.files.length<=0)){i.next=10;break}return i.abrupt("break",21);case 10:return l[f].uuid=Date.now(),i.next=13,(0,c.get_file_data)(l[f],t.fileMediatype);case 13:p=i.sent,p.progress=0,p.status="ready",t.files.push(p),o.push(d(d({},p),{},{file:l[f]}));case 18:f++,i.next=7;break;case 21:t.$emit("select",{tempFiles:o,tempFilePaths:a}),e.tempFiles=l,t.autoUpload&&!t.noSpace||(e.tempFiles=[]),e.tempFiles.forEach((function(e,i){t.provider&&(e.provider=t.provider);var n=e.name.split("."),r=n.pop(),s=n.join(".").replace(/[\s\/\?<>\\:\*\|":]/g,"_");e.cloudPath=s+"_"+Date.now()+"_"+i+"."+r}));case 25:case"end":return i.stop()}}),i)})))()},uploadFiles:function(e){var t=this;return e=[].concat(e),o.uploadCloudFiles.call(this,e,5,(function(e){t.setProgress(e,e.index,!0)})).then((function(e){return t.setSuccessAndError(e),e})).catch((function(e){console.log(e)}))},setSuccessAndError:function(e,t){var i=this;return(0,l.default)(s.default.mark((function t(){var n,r,u,a,l,o,c;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=[],r=[],u=[],a=[],l=s.default.mark((function t(l){var o,c,f;return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(o=e[l],c=o.uuid?i.files.findIndex((function(e){return e.uuid===o.uuid})):o.index,-1!==c&&i.files){t.next=4;break}return t.abrupt("return","break");case 4:if("request:fail"!==o.errMsg){t.next=12;break}i.files[c].url=o.path,i.files[c].status="error",i.files[c].errMsg=o.errMsg,r.push(i.files[c]),a.push(i.files[c].url),t.next=26;break;case 12:if(i.files[c].errMsg="",i.files[c].fileID=o.url,f=/cloud:\/\/([\w.]+\/?)\S*/,!f.test(o.url)){t.next=21;break}return t.next=18,i.getTempFileURL(o.url);case 18:i.files[c].url=t.sent,t.next=22;break;case 21:i.files[c].url=o.url;case 22:i.files[c].status="success",i.files[c].progress+=1,n.push(i.files[c]),u.push(i.files[c].fileID);case 26:case"end":return t.stop()}}),t)})),o=0;case 6:if(!(o<e.length)){t.next=14;break}return t.delegateYield(l(o),"t0",8);case 8:if(c=t.t0,"break"!==c){t.next=11;break}return t.abrupt("break",14);case 11:o++,t.next=6;break;case 14:n.length>0&&(i.setEmit(),i.$emit("success",{tempFiles:i.backObject(n),tempFilePaths:u})),r.length>0&&i.$emit("fail",{tempFiles:i.backObject(r),tempFilePaths:a});case 16:case"end":return t.stop()}}),t)})))()},setProgress:function(e,t,i){this.files.length;var n=Math.round(100*e.loaded/e.total),r=t;i||(r=this.files.findIndex((function(t){return t.uuid===e.tempFile.uuid}))),-1!==r&&this.files[r]&&(this.files[r].progress=n-1,this.$emit("progress",{index:r,progress:parseInt(n),tempFile:this.files[r]}))},delFile:function(e){var t=this;this.$emit("delete",{index:e,tempFile:this.files[e],tempFilePath:this.files[e].url}),this.files.splice(e,1),this.$nextTick((function(){t.setEmit()}))},getFileExt:function(e){var t=e.lastIndexOf("."),i=e.length;return{name:e.substring(0,t),ext:e.substring(t+1,i)}},setEmit:function(){var e=[];"object"===this.returnType?(e=this.backObject(this.files)[0],this.localValue=e||null):(e=this.backObject(this.files),this.localValue||(this.localValue=[]),this.localValue=(0,u.default)(e)),this.$emit("input",this.localValue)},backObject:function(e){var t=[];return e.forEach((function(e){t.push({extname:e.extname,fileType:e.fileType,image:e.image,name:e.name,path:e.path,size:e.size,fileID:e.fileID,url:e.url,uuid:e.uuid,status:e.status,cloudPath:e.cloudPath})})),t},getTempFileURL:function(t){return(0,l.default)(s.default.mark((function i(){var n;return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return t={fileList:[].concat(t)},i.next=3,e.getTempFileURL(t);case 3:return n=i.sent,i.abrupt("return",n.fileList[0].tempFileURL||"");case 5:case"end":return i.stop()}}),i)})))()},getForm:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"uniForms",t=this.$parent,i=t.$options.name;while(i!==e){if(t=t.$parent,!t)return!1;i=t.$options.name}return t}}};t.default=m}).call(this,i(287)["uniCloud"],i(2)["default"])},294:function(e,t,i){"use strict";i.r(t);var n=i(295),r=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){i.d(t,e,(function(){return n[e]}))}(s);t["default"]=r.a},295:function(e,t,i){}}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker-create-component',
    {
        'uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(282))
        })
    },
    [['uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker-create-component']]
]);
