@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.company-cell.data-v-0fd56b92 {
  width: 95%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
  padding: 24rpx;
  box-sizing: border-box;
  margin: 20rpx auto;
  background: #FFFFFF;
  height: auto;
}
.data-v-0fd56b92 .uni-list-item__container[data-v-296a3d7e] {
  flex-direction: column !important;
}
.header-cell.data-v-0fd56b92 {
  width: 100%;
  display: flex;
  justify-content: space-between;
}
.company-title.data-v-0fd56b92 {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
  width: 85%;
}
.company-info.data-v-0fd56b92 {
  display: flex;
  flex-direction: column;
  gap: 5px;
}
.info-item.data-v-0fd56b92 {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
}
.info-item .uni-icons.data-v-0fd56b92 {
  margin-right: 5px;
}
.detail-link.data-v-0fd56b92 {
  color: #999;
  font-size: 14px;
  display: flex;
  align-items: center;
}
.pulldown.data-v-0fd56b92 {
  height: calc(100vh - 120px);
}
.over-text.data-v-0fd56b92 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
