<view class="notice data-v-31563cfc"><view class="left_icon data-v-31563cfc"><slot name="left-icon"></slot></view><view class="right_notice data-v-31563cfc"><swiper class="notice_swiper data-v-31563cfc" vertical="{{true}}" easing-function="easeInOutCubic" autoplay="{{true}}" interval="3000"><block wx:for="{{noticeList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><swiper-item data-event-opts="{{[['tap',[['itemClick',['$0'],[[['noticeList','',index]]]]]]]}}" class="sw_item data-v-31563cfc" bindtap="__e"><text class="sw_text data-v-31563cfc" style="{{'color:'+(colors)+';'}}">{{item.title}}</text><view class="sw_image data-v-31563cfc"><view class="detail-txt data-v-31563cfc">详情<uni-icons vue-id="{{'0347cfc4-1-'+index}}" type="right" color="#FFAE00" size="17" class="data-v-31563cfc" bind:__l="__l"></uni-icons></view></view></swiper-item></block></swiper></view></view>