<template>
	<view class="dash-container custom-nav">
		<!-- 顶部城市风景图和标题 -->
		<view class="top-tips">本小程序已接入Deepseek智能服务</view>
		<view class="top-banner">
			<swiper class="top-img" :indicator-dots="true" indicator-active-color="#fff" circular :autoplay="true" :interval="3000"
				:duration="500">
				<swiper-item>
					<image class="top-img" src="https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914856679430684672.png"></image>
				</swiper-item>
				<swiper-item>
					<image class="top-img" src="https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914856855847305216.png"></image>
				</swiper-item>
				<swiper-item>
					<image class="top-img" src="https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914857044343521280.png"></image>
				</swiper-item>
				<swiper-item>
					<image class="top-img" src="https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914857249231077376.png"></image>
				</swiper-item>
				<swiper-item>
					<image class="top-img" src="https://bmhqpt.qzdsj.net/eos/policy/20250424/image/1915331732400242688.png"></image>
				</swiper-item>
				<swiper-item>
					<image class="top-img" src="https://bmhqpt.qzdsj.net/eos/policy/20250424/image/1915331947240882176.png"></image>
				</swiper-item>
			</swiper>
		</view>
		<!-- ai助手 -->
		<image @click="jumpAi" src="https://bmhqpt.qzdsj.net/eos/policy/20250425/image/1915567393153744896.png" class="ai-box" mode=""></image>
		<!-- 轮播通知 -->
		<view class="notice-swiper">
			<cc-noticeBar v-if="policyLists.length" :noticeList="policyLists" colors="#000000" @click="goDetailPolicy">
				<view slot="left-icon">
					<uni-icons type="sound-filled" color="#FFAE00" size="30"></uni-icons>
				</view>
				<!-- 				<view slot="right-btn">
					<view class="detail-txt">详情<uni-icons type="right" color="#FFAE00" size="17"></uni-icons></view>
				</view> -->
			</cc-noticeBar>
		</view>

		<!-- 功能区域 -->
		<view class="function-area">
			<view class="grid-item-box" @click="handlerAction('jydt')">
				<image class="jyzc-img" style="border-radius: 22upx;" src="../../static/bac/jydt.png" mode="aspectFit">
				</image>
			</view>
			<view class="grid-item-box" @click="handlerAction('wyqz')">
				<image class="jyzc-img" style="border-radius: 22upx;" src="../../static/bac/wyqz.png" mode="aspectFit">
				</image>
			</view>
		</view>
		<view class="jyzc-box" @click="handlerAction('jyzc')">
			<image class="jyzc-img" style="border-radius: 60upx;" src="../../static/bac/jyzc.png" mode="widthFix">
			</image>
		</view>
		<!-- 就业服务链接区域 -->
		<view class="service-links">
			<view class="service-title"></view>
			<view class="service-box">
				<!-- :class="[item.title.length > 6 ? 'service-item-cell' :'service-item']" -->
				<view class="service-item" v-for="(item, index) in serviceLinks" :key="index"
					@click="jumpService(item,index)">
					<image class="icon-service" :src="item.icon" mode=""></image>
					<view class="font-service">{{item.title}}</view>
				</view>
			</view>
		</view>

		<!-- 就业政策 -->
		<!-- 版本1 -->
		<view class="service-title-2" style="margin-left: 22upx"></view>
		<view class="hot-activities">
			<view class="tabs">
				<text @click="handerTab(0)" :class="['tab', activeTab == 0? 'active': '']">就业政策</text>
				<text @click="handerTab(1)" :class="['tab', activeTab == 1? 'active': '']">就业雷达</text>
			</view>
			<uni-list style="margin-top: 30upx;" v-if="activities.length">
				<cellVue v-for="(item, index) in activities" :obj="item" :key="index" @click="goDetail(item)" />
				<view class="more-box-1" @click="goJumpMore">更多 <uni-icons type="right" color="#CCCCCC"
						size="16"></uni-icons>
				</view>
			</uni-list>
			<view class="empty-box" v-else>
				暂无数据
			</view>
		</view>
		<!-- 版本2 -->
		<!-- 		<view style="display: flex;align-items: flex-end;margin-left: 32upx;">
			<view class="jy-tit"></view>
			<view class="more-box" @click="goJumpMore(0)">更多 <uni-icons type="right" color="#CCCCCC"
					size="16"></uni-icons>
			</view>
		</view>
		<view class="hot-activities">
			<uni-list style="margin-top: 30upx;" v-if="activities.length">
				<cellVue v-for="(item, index) in activities" :obj="item" :key="index" @click="goDetail(item,0)" />
			</uni-list>
			<view class="empty-box" v-else>
				暂无数据
			</view>
		</view>
		<view style="display: flex;align-items: flex-end;margin-left: 32upx;">
			<view class="jy-tit-2"></view>
			<view class="more-box" @click="goJumpMore(1)">更多 <uni-icons type="right" color="#CCCCCC"
					size="16"></uni-icons>
			</view>
		</view>
		<view class="hot-activities">
			<uni-list style="margin-top: 30upx;" v-if="jyLeiDa.length">
				<cellVue v-for="(item, index) in jyLeiDa" :obj="item" :key="index" @click="goDetail(item,1)" />
			</uni-list>
			<view class="empty-box" v-else>
				暂无数据
			</view>
		</view> -->
		<!-- footer -->
		<view class="footer-box">
			<text>主办单位名称：泉州市丰泽区人力资源和社会保障局</text>
			<text>投诉建议渠道：电话0595-22508200</text>
		</view>
	</view>
</template>

<script>
	import {
		getPolicyLists,
		getCompanyLists
	} from "../../api/rs"
	import cellVue from "../../components/cell.vue";
	import file from '../../mixins/file';
	export default {
		data() {
			return {
				serviceLinks: [{
						title: '福建人社',
						icon: require("../../static/icons/service-0.png"),
						webIdx: 0,
					},
					{
						title: '福建人才融媒',
						icon: require("../../static/icons/service-2.png"),
						webIdx: 1,
					},
					{
						title: '福建省流动人员档案公共服务平台',
						icon: require("../../static/icons/service-1.png"),
						webIdx: 5
					},
					{
						title: '福建省职业培训小助手',
						icon: require("../../static/icons/service-6.png"),
						webIdx: 7
					},
					{
						title: '泉州人才港湾',
						icon: require("../../static/icons/service-3.png"),
						webIdx: 2
					},
					// {
					// 	title: '大泉州人才网',
					// 	icon: require("../../static/icons/service-4.png"),
					// 	webIdx: 3
					// },
					{
						title: '海峡AI职途',
						icon: require("../../static/icons/service-4.png"),
						webIdx: 9
					},
					{
						title: '泉就业公共服务平台',
						icon: require("../../static/icons/service-5.png"),
						webIdx: 4
					},
					{
						title: '泉州就业和人才人事公共服务中心',
						icon: require("../../static/icons/service-6.png"),
						webIdx: 6
					}
				],
				policyLists: [],
				activities: [{
						title: '丰泽区"稳岗扩工"十五条措施10条内容',
						date: '2023-06-25'
					},
					{
						title: '青年人才集聚行动州州直部门开八条政策措施',
						date: '2023-06-23'
					},
					{
						title: '丰泽区高校毕业生就业创业政策',
						date: '2023-07-10'
					},
					{
						title: '丰泽区"稳岗扩工"十五条措施',
						date: '2023-06-25'
					}
				],
				jyLeiDa: [],
				noticeBarLists: [],
				activeTab: 0,
				noticeList: [{
						id: 1,
						title: '征程这些伟大精神 串连起中国共产党人的精神谱系'
					},
					{
						id: 2,
						title: '增强水运发展新动能 前5月港口货物吞吐量增长7.9%'
					},
					{
						id: 3,
						title: '多地持续高温 各地采取措施积极应对'
					},
					{
						id: 4,
						title: '中非经贸博览会见证中非合作深度'
					},
					{
						id: 5,
						title: '国安家安得民心 保驾护航促治兴'
					}
				],
			};
		},
		components: {
			cellVue
		},
		mixins: [file],
		methods: {
			handerTab(tab) {
				this.activeTab = tab
				this.fetchInit(tab)
			},
			jumpService(item, idx) {
				console.log("item", item)
				const url = `https://ai.enzenith.com/job/#/codePage`
				uni.navigateTo({
					url: `/bussinessPages/webview/webview?url=${url}&idx=${item.webIdx}&tit=${item.title}&useCode=1`
				})
			},
			// 获取政策列表和岗位列表
			async fetchInit(type) {
				switch (type) {
					case 0: {
						const res = await getPolicyLists({
							page: 1,
							pageSize: 5
						})
						console.log("政策列表", res)
						this.activities = res?.records.map(el => {
							return {
								...el,
								title: el.fileName.length > 21 ? el.fileName.substr(0, 21) + '...' : el
									.fileName,
								date: el.createTime
							}
						})
						this.policyLists = JSON.parse(JSON.stringify(this.activities))
					}
					break;
					case 1: {
						const res = await getCompanyLists({
							page: 1,
							pageSize: 5,
							firmStatus: 0,
							firmServiceObj: ''
						})
						console.log("公司列表", res)
						// jyLeiDa
						this.activities = res?.records.map(el => {
							return {
								...el,
								title: el.firmName.length > 23 ? el.firmName.substr(0, 23) + '...' : el
									.firmName,
								date: el.createTime
							}
						})
					}
					break;
					default: {}
					break;
				}
			},
			handlerAction(type) {
				// https://ai.enzenith.com
				// https://aiv2.enzenith.com
				const urls = {
					jydt: 'https://ai.enzenith.com/job/#/rsjmap',
					wyqz: 'https://ai.enzenith.com/job/#/rsjPolicyLeidaPage',
					jyzc: 'https://ai.enzenith.com/job/#/rsjPolicyPage'
				}
				// 版本1
				// const url = urls[type]
				// uni.navigateTo({
				// 	url: `/bussinessPages/webview/webview?url=${url}`,
				// })
				//版本2
				const urlPages = {
					jydt: '/bussinessPages/mapPage/mapPage',
					jyzc: '/bussinessPages/policyPage/policyPage',
					wyqz: '/bussinessPages/leidaPage/leidaPage',
					// https://aiv2.enzenith.com/job/#/rsjPolicyPage
				}
				switch (type) {
					case 'jydt':
					case 'wyqz':
					case 'jyzc': {
						uni.navigateTo({
							url: urlPages[type]
						})
					}
					break;
					// case 'wyqz': {
					// 	const url = urls[type]
					// 	uni.navigateTo({
					// 		url: `/bussinessPages/webview/webview?url=${url}`,
					// 	})
					// }
				}
			},
			jumpAi() {
				let path = 'https://ai.enzenith.com/job/#/home'
				// let path = 'https://aiv2.enzenith.com/job/#/home'
				uni.navigateTo({
					url: `/bussinessPages/webview/webview?url=${path}&code=Key_rsj`,
				})
			},
			goJumpMore(val) {
				const {
					activeTab
				} = this
				switch (activeTab) {
					case 0: {
						this.handlerAction('jyzc')
					}
					break;
					case 1: {
						this.handlerAction('wyqz')
					}
					break;
				}
			},
			goDetailPolicy(item) {
				this.handlerOpenPriviewFile(item)
			},
			goDetail(item, val) {
				const {
					activeTab
				} = this
				switch (Number(activeTab)) {
					case 0: {
						this.handlerOpenPriviewFile(item)
					}
					break;
					case 1: {
						const url = `https://ai.enzenith.com/job/#/rsjPolicyLeidaDetail`
						uni.navigateTo({
							url: `/bussinessPages/webview/webview?url=${url}&id=${item.id}`,
						})
					}
					break;
					default: {}
					break
				}
			}
		},
		onLoad() {
			this.fetchInit(0)
			this.fetchInit(1)
		}
	};
</script>

<style lang="scss">
	@import url("./index.scss");

	.empty-box {
		display: flex;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
		padding: 30upx;
		color: #8D94A2;
		font-size: 28upx;
	}

	.more-box-1 {
		color: #CED2DE;
		font-size: 26upx;
		display: flex;
		justify-content: center;
		box-sizing: border-box;
		padding-bottom: 12upx;
	}

	.footer-box {
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		box-sizing: border-box;
		// padding: 20upx 88upx;
		color: #8D94A2;
		font-size: 24upx;
		text-align: center;
		// font-size: 28upx;
	}
	::v-deep .uni-swiper-dot {
	    display: inline-block;
	    width: 22upx;
	    height: 5px;
	    cursor: pointer;
	    -webkit-transition-property: background-color;
	    transition-property: background-color;
	    -webkit-transition-timing-function: ease;
	    transition-timing-function: ease;
	    background: rgba(0,0,0,.3);
	    border-radius: 30upx;
	}
</style>