<template>
	<view class="home-container custom-nav">
		<!-- 顶部轮播图 -->
		<view class="banner-section">
			<swiper class="banner-swiper" :indicator-dots="true" indicator-active-color="#007AFF" circular :autoplay="true" :interval="3000" :duration="500">
				<swiper-item v-for="(item, index) in bannerList" :key="index">
					<image class="banner-image" :src="item.image" mode="aspectFill" @click="handleBannerClick(item)"></image>
				</swiper-item>
			</swiper>
		</view>
		
		<!-- 通知栏 -->
		<view class="notice-section" v-if="noticeList.length">
			<view class="notice-content">
				<uni-icons type="sound-filled" color="#FF9500" size="20"></uni-icons>
				<swiper class="notice-swiper" vertical :autoplay="true" :interval="3000" :duration="500">
					<swiper-item v-for="(notice, index) in noticeList" :key="index">
						<text class="notice-text" @click="handleNoticeClick(notice)">{{ notice.title }}</text>
					</swiper-item>
				</swiper>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="section-title">功能菜单</view>
			<view class="menu-grid">
				<view class="menu-item" v-for="(item, index) in menuList" :key="index" @click="handleMenuClick(item)">
					<view class="menu-icon">
						<image :src="item.icon" mode="aspectFit"></image>
					</view>
					<text class="menu-text">{{ item.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 快捷服务 -->
		<view class="quick-service">
			<view class="section-title">快捷服务</view>
			<view class="service-grid">
				<view class="service-item" v-for="(item, index) in serviceList" :key="index" @click="handleServiceClick(item)">
					<view class="service-icon">
						<image :src="item.icon" mode="aspectFit"></image>
					</view>
					<text class="service-text">{{ item.name }}</text>
				</view>
			</view>
		</view>
		
		<!-- 内容列表 -->
		<view class="content-section">
			<view class="section-title">最新动态</view>
			<uni-list>
				<uni-list-item 
					v-for="(item, index) in contentList" 
					:key="index"
					:title="item.title"
					:note="item.summary"
					:rightText="item.time"
					clickable
					@click="handleContentClick(item)"
				/>
			</uni-list>
			<view class="more-btn" @click="goToList">
				<text>查看更多</text>
				<uni-icons type="right" size="16" color="#999"></uni-icons>
			</view>
		</view>
	</view>
</template>

<script>
import { getDataList } from '@/api/rs.js'

export default {
	name: 'HomePage',
	data() {
		return {
			// 轮播图数据
			bannerList: [
				{
					id: 1,
					image: '/static/images/banner1.jpg',
					title: '轮播图1',
					url: ''
				},
				{
					id: 2,
					image: '/static/images/banner2.jpg', 
					title: '轮播图2',
					url: ''
				}
			],
			
			// 通知数据
			noticeList: [
				{
					id: 1,
					title: '这是一条重要通知信息'
				},
				{
					id: 2,
					title: '系统维护通知，请注意时间安排'
				}
			],
			
			// 功能菜单数据
			menuList: [
				{
					id: 1,
					name: '示例功能',
					icon: '/static/icons/demo.png',
					url: '/subPages/demo/demo'
				},
				{
					id: 2,
					name: '列表示例',
					icon: '/static/icons/list.png',
					url: '/subPages/list/list'
				},
				{
					id: 3,
					name: '表单示例',
					icon: '/static/icons/form.png',
					url: '/subPages/form/form'
				},
				{
					id: 4,
					name: '更多功能',
					icon: '/static/icons/more.png',
					url: ''
				}
			],
			
			// 快捷服务数据
			serviceList: [
				{
					id: 1,
					name: '在线客服',
					icon: '/static/icons/service.png'
				},
				{
					id: 2,
					name: '意见反馈',
					icon: '/static/icons/feedback.png'
				},
				{
					id: 3,
					name: '帮助中心',
					icon: '/static/icons/help.png'
				},
				{
					id: 4,
					name: '关于我们',
					icon: '/static/icons/about.png'
				}
			],
			
			// 内容列表数据
			contentList: []
		}
	},
	
	onLoad() {
		this.loadContentList()
	},
	
	methods: {
		// 轮播图点击
		handleBannerClick(item) {
			if (item.url) {
				uni.navigateTo({
					url: item.url
				})
			}
		},
		
		// 通知点击
		handleNoticeClick(notice) {
			uni.showModal({
				title: '通知详情',
				content: notice.title,
				showCancel: false
			})
		},
		
		// 菜单点击
		handleMenuClick(item) {
			if (item.url) {
				uni.navigateTo({
					url: item.url
				})
			} else {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				})
			}
		},
		
		// 服务点击
		handleServiceClick(item) {
			uni.showToast({
				title: `点击了${item.name}`,
				icon: 'none'
			})
		},
		
		// 内容点击
		handleContentClick(item) {
			uni.navigateTo({
				url: `/subPages/detail/detail?id=${item.id}`
			})
		},
		
		// 跳转到列表页
		goToList() {
			uni.navigateTo({
				url: '/subPages/list/list'
			})
		},
		
		// 加载内容列表
		async loadContentList() {
			try {
				// 这里可以调用实际的API
				// const result = await getDataList({ page: 1, pageSize: 5 })
				
				// 模拟数据
				this.contentList = [
					{
						id: 1,
						title: '示例内容标题1',
						summary: '这是内容的简要描述信息',
						time: '2024-01-01'
					},
					{
						id: 2,
						title: '示例内容标题2',
						summary: '这是另一条内容的简要描述',
						time: '2024-01-02'
					}
				]
			} catch (error) {
				console.error('加载内容失败：', error)
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.home-container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

.banner-section {
	height: 200px;
	margin-top: 88px;
}

.banner-swiper {
	height: 100%;
}

.banner-image {
	width: 100%;
	height: 100%;
}

.notice-section {
	background-color: #fff3cd;
	padding: 10px 15px;
	border-left: 4px solid #FF9500;
}

.notice-content {
	display: flex;
	align-items: center;
}

.notice-swiper {
	flex: 1;
	height: 30px;
	margin-left: 10px;
}

.notice-text {
	font-size: 14px;
	color: #856404;
	line-height: 30px;
}

.section-title {
	font-size: 18px;
	font-weight: bold;
	color: #333;
	margin-bottom: 15px;
}

.menu-section, .quick-service, .content-section {
	background-color: #ffffff;
	margin: 15px;
	padding: 20px;
	border-radius: 10px;
}

.menu-grid, .service-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.menu-item, .service-item {
	width: 22%;
	text-align: center;
	margin-bottom: 20px;
}

.menu-icon, .service-icon {
	width: 50px;
	height: 50px;
	margin: 0 auto 8px;
}

.menu-icon image, .service-icon image {
	width: 100%;
	height: 100%;
}

.menu-text, .service-text {
	font-size: 12px;
	color: #666;
}

.more-btn {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 15px;
	color: #999;
	font-size: 14px;
}
</style>
