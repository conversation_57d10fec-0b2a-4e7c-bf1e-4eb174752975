{"version": 3, "sources": ["webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue?b66c", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue?39e4", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue?c76e", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue?9f52", "uni-app:///uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue?f06c"], "names": ["renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "showSelector", "mixinDatacomResData", "length", "l0", "__map", "item", "index", "$orig", "__get_orig", "m0", "formatItemName", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "name", "mixins", "props", "localdata", "type", "default", "value", "modelValue", "label", "placeholder", "emptyTips", "clear", "defItem", "disabled", "format", "placement", "current", "apps", "channels", "cache<PERSON>ey", "created", "computed", "typePlaceholder", "common", "valueCom", "textShow", "getOffsetByPlacement", "watch", "immediate", "handler", "methods", "debounce", "args", "timer", "fn", "query", "onMixinDatacomPropsChange", "initDefVal", "strogeValue", "defValue", "isDisabled", "clearVal", "change", "emit", "toggleSelector", "text", "channel_code", "str", "getLoadData", "getCurrent<PERSON><PERSON><PERSON><PERSON>", "getCache", "setCache", "cacheData", "uni", "removeCache"], "mappings": "uKAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,6EACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,aAAeP,EAAIQ,oBAAoBC,OAAS,MACzDC,EACFV,EAAIO,cAAyB,IAAPH,EAClBJ,EAAIW,MAAMX,EAAIQ,qBAAqB,SAAUI,EAAMC,GACjD,IAAIC,EAAQd,EAAIe,WAAWH,GACvBI,EAAKhB,EAAIiB,eAAeL,GAC5B,MAAO,CACLE,MAAOA,EACPE,GAAIA,MAGR,KACNhB,EAAIkB,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLlB,GAAIA,EACJM,GAAIA,MAKRa,GAAmB,EACnBC,EAAkB,GACtBzB,EAAO0B,eAAgB,G,iCCtDvB,yHAAsyB,eAAG,G,kHCkCzyB,MAiBA,CACAC,uBACAC,4BACAC,OACAC,WACAC,WACAC,mBACA,WAGAC,OACAF,qBACAC,YAEAE,YACAH,qBACAC,YAEAG,OACAJ,YACAC,YAEAI,aACAL,YACAC,eAEAK,WACAN,YACAC,eAEAM,OACAP,aACAC,YAEAO,SACAR,YACAC,WAEAQ,UACAT,aACAC,YAGAS,QACAV,YACAC,YAEAU,WACAX,YACAC,mBAGAZ,gBACA,OACAZ,gBACAmC,WACAlC,uBACAmC,QACAC,YACAC,+CAGAC,mBAAA,WACA,2CACA,YACA,KACA,yCACA,oBAGAC,UACAC,2BACA,OACA,gCACA,2BACA,wBAEA,mBACA,qBACA,SACAC,IACAA,GAEAC,oBAKA,mBAGAC,oBAEA,mBACA,UAEAC,gCACA,uBACA,UACA,kCACA,aACA,kCAKAC,OACAxB,WACAyB,aACAC,sBACA,0BACA,8BAIAL,uBACA,mBAEA1C,qBACA8C,aACAC,oBACA,UACA,qBAMAC,SACAC,qBAAA,iEACA,OACA,+EAAAC,kBACA,mBACAC,yBACAC,eACA,KAIAC,iBACA,4BAGAC,qCACA,iBACA,oBAGAC,sBACA,SACA,qEAEA,CACA,MAIA,GAHA,kBACAC,mBAEA,SACAC,QACA,CACA,SACA,gEACA3B,kDAEA2B,KAEA,WACA,kBAhBAA,gBAmBA,uEACA,0CAOAC,uBACA,SAQA,OANA,8CACA,cACAA,gBAIA,GAGAC,oBACA,cACA,iBACA,oBAGAC,mBACA,YACA,qBACA,oCACA,qBAGAC,iBACA,sBACA,kCACA,uBACA,iBACA,kBAGAC,0BACA,gBAIA,uCAEArD,2BACA,IACAsD,EAGA3D,EAHA2D,KACAvC,EAEApB,EAFAoB,MACAwC,EACA5D,EADA4D,aAIA,GAFAA,yBAEA,aAEA,SAEA,aADAC,cACA,EACAA,oDAEA,SAEA,uDACAF,qBAEAA,GACAA,aACAC,IAKAE,uBACA,iCAGAC,8BACA,wBAGAC,oBAAA,uFACA,sCACA,aAGAC,qBAAA,uFACA,sCACAC,OACAC,mCAGAC,uBAAA,uFACA,6CACA,KACAD,qCAGA,c,gFCjUA,yHAAy9C,eAAG,G", "file": "uni_modules/uni-data-select/components/uni-data-select/uni-data-select.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-data-select.vue?vue&type=template&id=6b64008e&\"\nvar renderjs\nimport script from \"./uni-data-select.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-data-select.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-data-select.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-data-select.vue?vue&type=template&id=6b64008e&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.showSelector ? _vm.mixinDatacomResData.length : null\n  var l0 =\n    _vm.showSelector && !(g0 === 0)\n      ? _vm.__map(_vm.mixinDatacomResData, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.formatItemName(item)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-data-select.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-data-select.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-stat__select\">\r\n\t\t<span v-if=\"label\" class=\"uni-label-text hide-on-phone\">{{label + '：'}}</span>\r\n\t\t<view class=\"uni-stat-box\" :class=\"{'uni-stat__actived': current}\">\r\n\t\t\t<view class=\"uni-select\" :class=\"{'uni-select--disabled':disabled}\">\r\n\t\t\t\t<view class=\"uni-select__input-box\" @click=\"toggleSelector\">\r\n\t\t\t\t\t<view v-if=\"current\" class=\"uni-select__input-text\">{{textShow}}</view>\r\n\t\t\t\t\t<view v-else class=\"uni-select__input-text uni-select__input-placeholder\">{{typePlaceholder}}</view>\r\n\t\t\t\t\t<view v-if=\"current && clear && !disabled\" @click.stop=\"clearVal\">\r\n\t\t\t\t\t\t<uni-icons type=\"clear\" color=\"#c0c4cc\" size=\"24\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else>\r\n\t\t\t\t\t\t<uni-icons :type=\"showSelector? 'top' : 'bottom'\" size=\"14\" color=\"#999\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-select--mask\" v-if=\"showSelector\" @click=\"toggleSelector\" />\r\n\t\t\t\t<view class=\"uni-select__selector\" :style=\"getOffsetByPlacement\" v-if=\"showSelector\">\r\n\t\t\t\t\t<view :class=\"placement=='bottom'?'uni-popper__arrow_bottom':'uni-popper__arrow_top'\"></view>\r\n\t\t\t\t\t<scroll-view scroll-y=\"true\" class=\"uni-select__selector-scroll\">\r\n\t\t\t\t\t\t<view class=\"uni-select__selector-empty\" v-if=\"mixinDatacomResData.length === 0\">\r\n\t\t\t\t\t\t\t<text>{{emptyTips}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view v-else class=\"uni-select__selector-item\" v-for=\"(item,index) in mixinDatacomResData\" :key=\"index\"\r\n\t\t\t\t\t\t\t@click=\"change(item)\">\r\n\t\t\t\t\t\t\t<text :class=\"{'uni-select__selector__disabled': item.disable}\">{{formatItemName(item)}}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</scroll-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * DataChecklist 数据选择器\r\n\t * @description 通过数据渲染的下拉框组件\r\n\t * @tutorial https://uniapp.dcloud.io/component/uniui/uni-data-select\r\n\t * @property {String} value 默认值\r\n\t * @property {Array} localdata 本地数据 ，格式 [{text:'',value:''}]\r\n\t * @property {Boolean} clear 是否可以清空已选项\r\n\t * @property {Boolean} emptyText 没有数据时显示的文字 ，本地数据无效\r\n\t * @property {String} label 左侧标题\r\n\t * @property {String} placeholder 输入框的提示文字\r\n\t * @property {Boolean} disabled 是否禁用\r\n\t * @property {String} placement 弹出位置\r\n\t * \t@value top   \t\t顶部弹出\r\n\t * \t@value bottom\t\t底部弹出（default)\r\n\t * @event {Function} change  选中发生变化触发\r\n\t */\r\n\r\n\texport default {\r\n\t\tname: \"uni-data-select\",\r\n\t\tmixins: [uniCloud.mixinDatacom || {}],\r\n\t\tprops: {\r\n\t\t\tlocaldata: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tmodelValue: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tlabel: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tplaceholder: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '请选择'\r\n\t\t\t},\r\n\t\t\temptyTips: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '无选项'\r\n\t\t\t},\r\n\t\t\tclear: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tdefItem: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 格式化输出 用法 field=\"_id as value, version as text, uni_platform as label\" format=\"{label} - {text}\"\r\n\t\t\tformat: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tplacement: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'bottom'\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshowSelector: false,\r\n\t\t\t\tcurrent: '',\r\n\t\t\t\tmixinDatacomResData: [],\r\n\t\t\t\tapps: [],\r\n\t\t\t\tchannels: [],\r\n\t\t\t\tcacheKey: \"uni-data-select-lastSelectedValue\",\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.debounceGet = this.debounce(() => {\r\n\t\t\t\tthis.query();\r\n\t\t\t}, 300);\r\n\t\t\tif (this.collection && !this.localdata.length) {\r\n\t\t\t\tthis.debounceGet();\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ttypePlaceholder() {\r\n\t\t\t\tconst text = {\r\n\t\t\t\t\t'opendb-stat-app-versions': '版本',\r\n\t\t\t\t\t'opendb-app-channels': '渠道',\r\n\t\t\t\t\t'opendb-app-list': '应用'\r\n\t\t\t\t}\r\n\t\t\t\tconst common = this.placeholder\r\n\t\t\t\tconst placeholder = text[this.collection]\r\n\t\t\t\treturn placeholder ?\r\n\t\t\t\t\tcommon + placeholder :\r\n\t\t\t\t\tcommon\r\n\t\t\t},\r\n\t\t\tvalueCom() {\r\n\t\t\t\t// #ifdef VUE3\r\n\t\t\t\treturn this.modelValue;\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef VUE3\r\n\t\t\t\treturn this.value;\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\ttextShow() {\r\n\t\t\t\t// 长文本显示\r\n\t\t\t\tlet text = this.current;\r\n\t\t\t\treturn text;\r\n\t\t\t},\r\n\t\t\tgetOffsetByPlacement() {\r\n\t\t\t\tswitch (this.placement) {\r\n\t\t\t\t\tcase 'top':\r\n\t\t\t\t\t\treturn \"bottom:calc(100% + 12px);\";\r\n\t\t\t\t\tcase 'bottom':\r\n\t\t\t\t\t\treturn \"top:calc(100% + 12px);\";\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\twatch: {\r\n\t\t\tlocaldata: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(val, old) {\r\n\t\t\t\t\tif (Array.isArray(val) && old !== val) {\r\n\t\t\t\t\t\tthis.mixinDatacomResData = val\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tvalueCom(val, old) {\r\n\t\t\t\tthis.initDefVal()\r\n\t\t\t},\r\n\t\t\tmixinDatacomResData: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(val) {\r\n\t\t\t\t\tif (val.length) {\r\n\t\t\t\t\t\tthis.initDefVal()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tdebounce(fn, time = 100) {\r\n\t\t\t\tlet timer = null\r\n\t\t\t\treturn function(...args) {\r\n\t\t\t\t\tif (timer) clearTimeout(timer)\r\n\t\t\t\t\ttimer = setTimeout(() => {\r\n\t\t\t\t\t\tfn.apply(this, args)\r\n\t\t\t\t\t}, time)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 执行数据库查询\r\n\t\t\tquery() {\r\n\t\t\t\tthis.mixinDatacomEasyGet();\r\n\t\t\t},\r\n\t\t\t// 监听查询条件变更事件\r\n\t\t\tonMixinDatacomPropsChange() {\r\n\t\t\t\tif (this.collection) {\r\n\t\t\t\t\tthis.debounceGet();\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinitDefVal() {\r\n\t\t\t\tlet defValue = ''\r\n\t\t\t\tif ((this.valueCom || this.valueCom === 0) && !this.isDisabled(this.valueCom)) {\r\n\t\t\t\t\tdefValue = this.valueCom\r\n\t\t\t\t} else {\r\n\t\t\t\t\tlet strogeValue\r\n\t\t\t\t\tif (this.collection) {\r\n\t\t\t\t\t\tstrogeValue = this.getCache()\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (strogeValue || strogeValue === 0) {\r\n\t\t\t\t\t\tdefValue = strogeValue\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tlet defItem = ''\r\n\t\t\t\t\t\tif (this.defItem > 0 && this.defItem <= this.mixinDatacomResData.length) {\r\n\t\t\t\t\t\t\tdefItem = this.mixinDatacomResData[this.defItem - 1].value\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tdefValue = defItem\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (defValue || defValue === 0) {\r\n\t\t\t\t\t\tthis.emit(defValue)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tconst def = this.mixinDatacomResData.find(item => item.value === defValue)\r\n\t\t\t\tthis.current = def ? this.formatItemName(def) : ''\r\n\t\t\t},\r\n\r\n\t\t\t/**\r\n\t\t\t * @param {[String, Number]} value\r\n\t\t\t * 判断用户给的 value 是否同时为禁用状态\r\n\t\t\t */\r\n\t\t\tisDisabled(value) {\r\n\t\t\t\tlet isDisabled = false;\r\n\r\n\t\t\t\tthis.mixinDatacomResData.forEach(item => {\r\n\t\t\t\t\tif (item.value === value) {\r\n\t\t\t\t\t\tisDisabled = item.disable\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\r\n\t\t\t\treturn isDisabled;\r\n\t\t\t},\r\n\r\n\t\t\tclearVal() {\r\n\t\t\t\tthis.emit('')\r\n\t\t\t\tif (this.collection) {\r\n\t\t\t\t\tthis.removeCache()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchange(item) {\r\n\t\t\t\tif (!item.disable) {\r\n\t\t\t\t\tthis.showSelector = false\r\n\t\t\t\t\tthis.current = this.formatItemName(item)\r\n\t\t\t\t\tthis.emit(item.value)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\temit(val) {\r\n\t\t\t\tthis.$emit('input', val)\r\n\t\t\t\tthis.$emit('update:modelValue', val)\r\n\t\t\t\tthis.$emit('change', val)\r\n\t\t\t\tif (this.collection) {\r\n\t\t\t\t\tthis.setCache(val);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttoggleSelector() {\r\n\t\t\t\tif (this.disabled) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\r\n\t\t\t\tthis.showSelector = !this.showSelector\r\n\t\t\t},\r\n\t\t\tformatItemName(item) {\r\n\t\t\t\tlet {\r\n\t\t\t\t\ttext,\r\n\t\t\t\t\tvalue,\r\n\t\t\t\t\tchannel_code\r\n\t\t\t\t} = item\r\n\t\t\t\tchannel_code = channel_code ? `(${channel_code})` : ''\r\n\r\n\t\t\t\tif (this.format) {\r\n\t\t\t\t\t// 格式化输出\r\n\t\t\t\t\tlet str = \"\";\r\n\t\t\t\t\tstr = this.format;\r\n\t\t\t\t\tfor (let key in item) {\r\n\t\t\t\t\t\tstr = str.replace(new RegExp(`{${key}}`, \"g\"), item[key]);\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn str;\r\n\t\t\t\t} else {\r\n\t\t\t\t\treturn this.collection.indexOf('app-list') > 0 ?\r\n\t\t\t\t\t\t`${text}(${value})` :\r\n\t\t\t\t\t\t(\r\n\t\t\t\t\t\t\ttext ?\r\n\t\t\t\t\t\t\ttext :\r\n\t\t\t\t\t\t\t`未命名${channel_code}`\r\n\t\t\t\t\t\t)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取当前加载的数据\r\n\t\t\tgetLoadData() {\r\n\t\t\t\treturn this.mixinDatacomResData;\r\n\t\t\t},\r\n\t\t\t// 获取当前缓存key\r\n\t\t\tgetCurrentCacheKey() {\r\n\t\t\t\treturn this.collection;\r\n\t\t\t},\r\n\t\t\t// 获取缓存\r\n\t\t\tgetCache(name = this.getCurrentCacheKey()) {\r\n\t\t\t\tlet cacheData = uni.getStorageSync(this.cacheKey) || {};\r\n\t\t\t\treturn cacheData[name];\r\n\t\t\t},\r\n\t\t\t// 设置缓存\r\n\t\t\tsetCache(value, name = this.getCurrentCacheKey()) {\r\n\t\t\t\tlet cacheData = uni.getStorageSync(this.cacheKey) || {};\r\n\t\t\t\tcacheData[name] = value;\r\n\t\t\t\tuni.setStorageSync(this.cacheKey, cacheData);\r\n\t\t\t},\r\n\t\t\t// 删除缓存\r\n\t\t\tremoveCache(name = this.getCurrentCacheKey()) {\r\n\t\t\t\tlet cacheData = uni.getStorageSync(this.cacheKey) || {};\r\n\t\t\t\tdelete cacheData[name];\r\n\t\t\t\tuni.setStorageSync(this.cacheKey, cacheData);\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t$uni-base-color: #6a6a6a !default;\r\n\t$uni-main-color: #333 !default;\r\n\t$uni-secondary-color: #909399 !default;\r\n\t$uni-border-3: #e5e5e5;\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t@media screen and (max-width: 500px) {\r\n\t\t.hide-on-phone {\r\n\t\t\tdisplay: none;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n\t.uni-stat__select {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t// padding: 15px;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t\twidth: 100%;\r\n\t\tflex: 1;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.uni-stat-box {\n\t\tbackground-color: #fff;\r\n\t\twidth: 100%;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.uni-stat__actived {\r\n\t\twidth: 100%;\r\n\t\tflex: 1;\r\n\t\t// outline: 1px solid #2979ff;\r\n\t}\r\n\r\n\t.uni-label-text {\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: $uni-base-color;\r\n\t\tmargin: auto 0;\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.uni-select {\r\n\t\tfont-size: 14px;\r\n\t\tborder: 1px solid $uni-border-3;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 4px;\r\n\t\tpadding: 0 5px;\r\n\t\tpadding-left: 10px;\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tuser-select: none;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: solid 1px $uni-border-3;\r\n\t\twidth: 100%;\r\n\t\tflex: 1;\r\n\t\theight: 35px;\r\n\r\n\t\t&--disabled {\r\n\t\t\tbackground-color: #f5f7fa;\r\n\t\t\tcursor: not-allowed;\r\n\t\t}\r\n\t}\r\n\r\n\t.uni-select__label {\r\n\t\tfont-size: 16px;\r\n\t\t// line-height: 22px;\r\n\t\theight: 35px;\r\n\t\tpadding-right: 10px;\r\n\t\tcolor: $uni-secondary-color;\r\n\t}\r\n\r\n\t.uni-select__input-box {\r\n\t\theight: 35px;\n\t\twidth: 0px;\r\n\t\tposition: relative;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\n\t}\r\n\r\n\t.uni-select__input {\r\n\t\tflex: 1;\r\n\t\tfont-size: 14px;\r\n\t\theight: 22px;\r\n\t\tline-height: 22px;\r\n\t}\r\n\r\n\t.uni-select__input-plac {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: $uni-secondary-color;\r\n\t}\r\n\r\n\t.uni-select__selector {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\t\tposition: absolute;\r\n\t\tleft: 0;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder: 1px solid #EBEEF5;\r\n\t\tborder-radius: 6px;\r\n\t\tbox-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n\t\tz-index: 3;\r\n\t\tpadding: 4px 0;\r\n\t}\r\n\r\n\t.uni-select__selector-scroll {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tmax-height: 200px;\r\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t/* #ifdef H5 */\r\n\t@media (min-width: 768px) {\r\n\t\t.uni-select__selector-scroll {\r\n\t\t\tmax-height: 600px;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n\r\n\t.uni-select__selector-empty,\r\n\t.uni-select__selector-item {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t\tline-height: 35px;\r\n\t\tfont-size: 14px;\r\n\t\ttext-align: center;\r\n\t\t/* border-bottom: solid 1px $uni-border-3; */\r\n\t\tpadding: 0px 10px;\r\n\t}\r\n\r\n\t.uni-select__selector-item:hover {\r\n\t\tbackground-color: #f9f9f9;\r\n\t}\r\n\r\n\t.uni-select__selector-empty:last-child,\r\n\t.uni-select__selector-item:last-child {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tborder-bottom: none;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-select__selector__disabled {\r\n\t\topacity: 0.4;\r\n\t\tcursor: default;\r\n\t}\r\n\r\n\t/* picker 弹出层通用的指示小三角 */\r\n\t.uni-popper__arrow_bottom,\r\n\t.uni-popper__arrow_bottom::after,\r\n\t.uni-popper__arrow_top,\r\n\t.uni-popper__arrow_top::after,\r\n\t{\r\n\tposition: absolute;\r\n\tdisplay: block;\r\n\twidth: 0;\r\n\theight: 0;\r\n\tborder-color: transparent;\r\n\tborder-style: solid;\r\n\tborder-width: 6px;\r\n\t}\r\n\r\n\t.uni-popper__arrow_bottom {\r\n\t\tfilter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));\r\n\t\ttop: -6px;\r\n\t\tleft: 10%;\r\n\t\tmargin-right: 3px;\r\n\t\tborder-top-width: 0;\r\n\t\tborder-bottom-color: #EBEEF5;\r\n\t}\r\n\r\n\t.uni-popper__arrow_bottom::after {\r\n\t\tcontent: \" \";\r\n\t\ttop: 1px;\r\n\t\tmargin-left: -6px;\r\n\t\tborder-top-width: 0;\r\n\t\tborder-bottom-color: #fff;\r\n\t}\r\n\r\n\t.uni-popper__arrow_top {\r\n\t\tfilter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));\r\n\t\tbottom: -6px;\r\n\t\tleft: 10%;\r\n\t\tmargin-right: 3px;\r\n\t\tborder-bottom-width: 0;\r\n\t\tborder-top-color: #EBEEF5;\r\n\t}\r\n\r\n\t.uni-popper__arrow_top::after {\r\n\t\tcontent: \" \";\r\n\t\tbottom: 1px;\r\n\t\tmargin-left: -6px;\r\n\t\tborder-bottom-width: 0;\r\n\t\tborder-top-color: #fff;\r\n\t}\r\n\r\n\r\n\t.uni-select__input-text {\r\n\t\t// width: 280px;\r\n\t\twidth: 100%;\r\n\t\tcolor: $uni-main-color;\r\n\t\twhite-space: nowrap;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t-o-text-overflow: ellipsis;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-select__input-placeholder {\r\n\t\tcolor: $uni-base-color;\r\n\t\tfont-size: 12px;\r\n\t}\r\n\r\n\t.uni-select--mask {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tbottom: 0;\r\n\t\tright: 0;\r\n\t\tleft: 0;\r\n\t\tz-index: 2;\r\n\t}\r\n</style>\n", "import mod from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-data-select.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uni-data-select.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}