@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.cell-container.data-v-49b0461d {
  background-color: #f0f7ff;
  /* 浅蓝色背景 */
  border-radius: 8px;
  margin-bottom: 10px;
}
.cell-content.data-v-49b0461d {
  background-color: #ffffff;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.cell-icon.data-v-49b0461d {
  width: 40px;
  height: 40px;
  margin-right: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.icon-image.data-v-49b0461d {
  width: 30px;
  height: 30px;
}
.cell-info.data-v-49b0461d {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.cell-title.data-v-49b0461d {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: 4px;
  /* 超出两行显示省略号 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}
.cell-date.data-v-49b0461d {
  font-size: 12px;
  color: #999999;
}
