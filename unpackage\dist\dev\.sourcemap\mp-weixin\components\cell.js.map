{"version": 3, "sources": ["webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/cell.vue?1b2b", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/cell.vue?1ce5", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/cell.vue?5278", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/cell.vue?8c6e", "uni-app:///components/cell.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/cell.vue?0461"], "names": ["renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "name", "props", "title", "type", "default", "date", "docId", "obj", "methods", "handleClick"], "mappings": "gHAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,sBACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAA8uB,eAAG,G,yGCejvB,CACAC,eACAC,OAEAC,OACAC,YACAC,YAGAC,MACAF,YACAC,YAGAE,OACAH,qBACAC,YAEAG,KACAJ,YACAC,uBAGAI,SAEAC,uBACA,gCAGA,a,iCC5CA,yHAAq5C,eAAG,G", "file": "components/cell.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cell.vue?vue&type=template&id=49b0461d&scoped=true&\"\nvar renderjs\nimport script from \"./cell.vue?vue&type=script&lang=js&\"\nexport * from \"./cell.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cell.vue?vue&type=style&index=0&id=49b0461d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"49b0461d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/cell.vue\"\nexport default component.exports", "export * from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cell.vue?vue&type=template&id=49b0461d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cell.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cell.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"cell-container\" @click=\"handleClick\">\n    <view class=\"cell-content\">\n      <view class=\"cell-icon\">\n        <image src=\"/static/icons/todo.png\" mode=\"aspectFit\" class=\"icon-image\"></image>\n      </view>\n      <view class=\"cell-info\">\n        <text class=\"cell-title\">{{ obj.title }}</text>\n        <text class=\"cell-date\">{{ obj.date }}</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'DocCell',\n  props: {\n    // 文档标题\n    title: {\n      type: String,\n      default: ''\n    },\n    // 文档日期\n    date: {\n      type: String,\n      default: ''\n    },\n    // 文档链接或ID\n    docId: {\n      type: [String, Number],\n      default: ''\n    },\r\n\tobj: {\r\n\t\ttype: Object,\r\n\t\tdefault: () => {}\r\n\t}\n  },\n  methods: {\n    // 点击文档卡片时触发\n    handleClick() {\n      this.$emit('click', this.obj);\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.cell-container {\n  background-color: #f0f7ff; /* 浅蓝色背景 */\n  border-radius: 8px;\n  // padding: 10px;\n  margin-bottom: 10px;\n}\n\n.cell-content {\n  background-color: #ffffff;\n  border-radius: 6px;\n  padding: 12px;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n}\n\n.cell-icon {\n  width: 40px;\n  height: 40px;\n  margin-right: 10px;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.icon-image {\n  width: 30px;\n  height: 30px;\n}\n\n.cell-info {\n  flex: 1;\n  display: flex;\n  flex-direction: column;\n}\n\n.cell-title {\n  font-size: 14px;\n  color: #333333;\n  font-weight: 500;\n  line-height: 1.4;\n  margin-bottom: 4px;\n  /* 超出两行显示省略号 */\n  overflow: hidden;\n  text-overflow: ellipsis;\n  display: -webkit-box;\n  -webkit-line-clamp: 2;\n  -webkit-box-orient: vertical;\n}\n\n.cell-date {\n  font-size: 12px;\n  color: #999999;\n}\n</style>", "import mod from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cell.vue?vue&type=style&index=0&id=49b0461d&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cell.vue?vue&type=style&index=0&id=49b0461d&lang=scss&scoped=true&\""], "sourceRoot": ""}