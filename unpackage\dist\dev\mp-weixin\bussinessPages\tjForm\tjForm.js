(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/tjForm/tjForm"],{102:function(e,n,t){"use strict";(function(e,n){var r=t(4);t(26);r(t(25));var o=r(t(103));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t(1)["default"],t(2)["createPage"])},103:function(e,n,t){"use strict";t.r(n);var r=t(104),o=t(106);for(var a in o)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(a);t(108);var i,u=t(32),s=Object(u["default"])(o["default"],r["render"],r["staticRenderFns"],!1,null,"e5d6fb76",null,!1,r["components"],i);s.options.__file="bussinessPages/tjForm/tjForm.vue",n["default"]=s.exports},104:function(e,n,t){"use strict";t.r(n);var r=t(105);t.d(n,"render",(function(){return r["render"]})),t.d(n,"staticRenderFns",(function(){return r["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return r["recyclableRender"]})),t.d(n,"components",(function(){return r["components"]}))},105:function(e,n,t){"use strict";var r;t.r(n),t.d(n,"render",(function(){return o})),t.d(n,"staticRenderFns",(function(){return i})),t.d(n,"recyclableRender",(function(){return a})),t.d(n,"components",(function(){return r}));try{r={uniForms:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(t.bind(null,262))},uniFormsItem:function(){return t.e("uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(t.bind(null,275))},uniEasyinput:function(){return t.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(t.bind(null,296))},uniDataSelect:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-data-select/components/uni-data-select/uni-data-select")]).then(t.bind(null,330))},uniFilePicker:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker")]).then(t.bind(null,282))}}}catch(u){if(-1===u.message.indexOf("Cannot find module")||-1===u.message.indexOf(".vue"))throw u;console.error(u.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var e=this,n=e.$createElement,t=(e._self._c,e.formData.intentionPersonName?e.formData.intentionPersonName.length:null);e.$mp.data=Object.assign({},{$root:{g0:t}})},a=!1,i=[];o._withStripped=!0},106:function(e,n,t){"use strict";t.r(n);var r=t(107),o=t.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(a);n["default"]=o.a},107:function(e,n,t){"use strict";(function(e){var r=t(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=r(t(37)),a=r(t(11)),i=r(t(39)),u=(t(99),t(47));function s(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?s(Object(t),!0).forEach((function(n){(0,a.default)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):s(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var l=t(34),f={data:function(){return{formData:{firmManagementName:"",firmManagementPostId:"",firmManagementPostName:"",intentionPersonName:"",intentionPersonPhone:"",intentionPersonFile:"",firmStreet:""},userInfo:null,companyId:"",photo:[],items:[{text:"一年级",value:"1-0",children:[{text:"1.1班",value:"1-1"},{text:"1.2班",value:"1-2"}]},{text:"二年级",value:"2-0"},{text:"三年级",value:"3-0"}],jobList:[],rules:{intentionPersonName:{rules:[{required:!0,errorMessage:"请输入姓名"},{maxLength:100,errorMessage:"姓名长度最长 {maxLength} 个字符"}]},sex:{rules:[{required:!0,errorMessage:"请选择性别"}]},birthdate:{rules:[{required:!0,errorMessage:"请选择出生年月日"}]},firmManagementPostId:{rules:[{required:!0,errorMessage:"请选择求职意向"}]},educationLevel:{rules:[{required:!0,errorMessage:"请选择学历水平"}]},desiredField:{rules:[{required:!0,errorMessage:"请选择求职意向"}]}}}},computed:{},watch:{formData:{handler:function(e){},deep:!0,immediate:!0}},methods:{initDict:function(){var e=this;return(0,i.default)(o.default.mark((function n(){var t;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,u.getPostListNames)({firmManagementId:e.companyId,status:1});case 2:t=n.sent,console.log("jobList",t),e.jobList=null===t||void 0===t?void 0:t.map((function(e){return{value:e.id,text:e.postName}})),e.formData.firmManagementPostId&&e.postChange(e.formData.firmManagementPostId);case 6:case"end":return n.stop()}}),n)})))()},postChange:function(e){var n=this.jobList.filter((function(n){return n.value==e}));console.log("postChange",n),this.formData.firmManagementPostName=n.length?n[0].text:""},formatTreeData:function(e){var n=this,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"name",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"id",o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"children";return e&&Array.isArray(e)?e.map((function(e){var a=c(c({},e),{},{text:e[t]||"",value:e[r]||""});return e[o]&&Array.isArray(e[o])&&(a[o]=n.formatTreeData(e[o],t,r,o)),a})):[]},onnodeclick:function(e){},submitForm:function(){var n=this,t=this.formData;console.log("formData",t),this.$refs.formVal.validate().then(function(){var r=(0,i.default)(o.default.mark((function r(a){var i;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return console.log("表单数据信息：",a),r.next=3,(0,u.addIntention)(c(c({},t),{},{firmManagementId:n.companyId,wxUserId:n.userInfo.userId}));case 3:i=r.sent,console.log("修改用户信息",i),i&&(e.showToast({title:"提交成功",icon:"success"}),setTimeout((function(){e.navigateBack()}),500));case 6:case"end":return r.stop()}}),r)})));return function(e){return r.apply(this,arguments)}}()).catch((function(e){console.log("表单错误信息：",e)}))},fileSelectDiy:function(n){var t=this;return(0,i.default)(o.default.mark((function r(){var a,i,u,s;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:a=n.tempFiles,i=n.tempFilePaths,u=l.baseApi,s="".concat(u,"/api/v1/common/uploadAvatar"),console.log("diy",a,i,l,s),a[0].size>20971520?(e.showToast({title:"文件大小不可超过20MB",icon:"none"}),t.$refs.uploadFile.clearFiles(0)):["jpg","jpeg","png","gif","bmp","webp","tiff","svg","pdf","doc","docx"].includes(a[0].extname)?e.uploadFile({url:s,filePath:i[0],name:"file",header:{Authorization:e.getStorageSync("userToken")},formData:{file:a[0].file},success:function(e){var n=JSON.parse(e.data);console.log(n),t.formData.intentionPersonFile=(null===n||void 0===n?void 0:n.data.path)||""},fail:function(e){t.formData.intentionPersonFile="",console.error(e)}}):(e.showToast({title:"请上传正确文件格式",icon:"none"}),t.$refs.uploadFile.clearFiles(0));case 5:case"end":return r.stop()}}),r)})))()}},onReady:function(){var e,n;null===(e=this.$refs)||void 0===e||null===(n=e.formVal)||void 0===n||n.setRules(this.rules)},onShow:function(){return(0,i.default)(o.default.mark((function e(){return o.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()},onLoad:function(n){var t=this;return(0,i.default)(o.default.mark((function r(){var a,i,u;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.companyId=n.companyId,t.formData.firmManagementName=[null,"null"].includes(n.cName)?"-":n.cName,u=e.getStorageSync("userInfo"),t.userInfo=JSON.parse(u),t.formData.intentionPersonName=(null===(a=t.userInfo)||void 0===a?void 0:a.nickName)||"",t.formData.intentionPersonPhone=(null===(i=t.userInfo)||void 0===i?void 0:i.phone)||"",console.log("options",n),n.gwId&&(t.formData.firmManagementPostId=n.gwId),t.formData.firmStreet=n.firmStreet||"",t.initDict();case 10:case"end":return r.stop()}}),r)})))()}};n.default=f}).call(this,t(2)["default"])},108:function(e,n,t){"use strict";t.r(n);var r=t(109),o=t.n(r);for(var a in r)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(a);n["default"]=o.a},109:function(e,n,t){}},[[102,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/bussinessPages/tjForm/tjForm.js.map