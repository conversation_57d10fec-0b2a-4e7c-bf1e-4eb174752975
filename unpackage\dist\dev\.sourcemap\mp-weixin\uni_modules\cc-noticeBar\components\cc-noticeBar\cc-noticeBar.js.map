{"version": 3, "sources": ["webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue?0979", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue?25d8", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue?9b5d", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue?8c9f", "uni-app:///uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue?2303"], "names": ["renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "props", "colors", "type", "default", "noticeList", "methods", "itemClick"], "mappings": "8JAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,oEACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCjCvB,yHAAmyB,eAAG,G,yGCwBtyB,CACAC,gBACA,UAGAjB,cACAkB,OACAC,QACAC,YACAC,gBAEAC,YACAF,aAGAG,SACAC,sBAEA,yBAGA,a,iCC7CA,yHAA8+C,eAAG,G", "file": "uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cc-noticeBar.vue?vue&type=template&id=31563cfc&scoped=true&\"\nvar renderjs\nimport script from \"./cc-noticeBar.vue?vue&type=script&lang=js&\"\nexport * from \"./cc-noticeBar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./cc-noticeBar.vue?vue&type=style&index=0&id=31563cfc&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"31563cfc\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cc-noticeBar.vue?vue&type=template&id=31563cfc&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cc-noticeBar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cc-noticeBar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"notice\">\r\n\r\n\t\t<!-- <image class=\"left_icon\" mode=\"aspectFit\" src=\"./notice_icon.png\"> -->\r\n\t\t<view class=\"left_icon\">\r\n\t\t\t<slot name=\"left-icon\"></slot>\r\n\t\t</view>\r\n\t\t</image>\r\n\t\t<view class=\"right_notice\">\r\n\t\t\t<swiper class=\"notice_swiper\" vertical easing-function=\"easeInOutCubic\" autoplay interval=\"3000\">\r\n\t\t\t\t<swiper-item v-for=\"(item,index) in noticeList\" :key=\"index\" class=\"sw_item\" @click=\"itemClick(item)\">\r\n\t\t\t\t\t<text class=\"sw_text\" :style=\"{color:colors}\">{{item.title}}</text>\r\n\t\t\t\t\t<!-- <image class=\"sw_image\" src=\"/static/images/home/<USER>\"></image> -->\r\n\t\t\t\t\t<view class=\"sw_image\">\r\n\t\t\t\t\t\t<!-- <slot name=\"right-btn\"></slot> -->\r\n\t\t\t\t\t\t<view class=\"detail-txt\">详情<uni-icons type=\"right\" color=\"#FFAE00\" size=\"17\"></uni-icons></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {};\r\n\t\t},\r\n\r\n\t\tcomponents: {},\r\n\t\tprops: {\r\n\t\t\tcolors: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#333'\r\n\t\t\t},\r\n\t\t\tnoticeList: {\r\n\t\t\t\ttype: Array\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\titemClick(item) {\r\n\r\n\t\t\t\tthis.$emit('click', item);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.notice {\r\n\t\theight: 80upx;\r\n\t\tline-height: 80upx;\r\n\t\tmargin: 0 3%;\r\n\t\tmargin-top: 15upx;\r\n\t\tpadding: 0 10upx;\r\n\t\tbox-shadow: 0upx 0upx 10upx #eee;\r\n\t\tborder-radius: 32upx;\r\n\t\tbackground: #fff;\r\n\t}\r\n\t.detail-txt{\r\n\t\tfont-weight: 400;\r\n\t\tfont-size: 24upx;\r\n\t\tcolor: #FFAE00;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\t.left_icon {\r\n\r\n\t\twidth: 10%;\r\n\t\theight: 24px;\r\n\t\tfloat: left;\r\n\t\t// padding-top: 18upx;\r\n\t}\r\n\r\n\t.left_icon .iconfont {\r\n\t\tdisplay: inline-block;\r\n\t\tfont-size: 44upx;\r\n\t}\r\n\r\n\t.right_notice {\r\n\t\tfloat: left;\r\n\t\twidth: 90%;\r\n\r\n\t}\r\n\r\n\t.right_notice .notice_swiper {\r\n\t\theight: 80upx;\r\n\t}\r\n\r\n\t.notice_swiper .sw_item {\r\n\t\theight: 80upx;\r\n\t}\r\n\r\n\t.notice_swiper .sw_item .sw_text {\r\n\t\tfont-size: 24upx;\r\n\t\tcolor: #333;\r\n\t\tdisplay: inline-block;\r\n\t\twidth: 81%;\r\n\t}\r\n\r\n\t.notice_swiper .sw_image {\r\n\t\twidth: 90upx;\r\n\t\theight: 40upx;\r\n\t\tfloat: right;\r\n\t\t// margin-top: 20upx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cc-noticeBar.vue?vue&type=style&index=0&id=31563cfc&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./cc-noticeBar.vue?vue&type=style&index=0&id=31563cfc&lang=scss&scoped=true&\""], "sourceRoot": ""}