{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/pldDetail.vue?6796", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/pldDetail.vue?c4a1", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/pldDetail.vue?e582", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/pldDetail.vue?8c09", "uni-app:///bussinessPages/pldDetail/pldDetail.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/pldDetail.vue?2e32"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniIcons", "zPaging", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "l0", "_self", "_c", "__map", "dataList", "cell", "idx", "$orig", "__get_orig", "a0", "Object", "assign", "g0", "length", "$mp", "data", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "name", "leidaCellVue", "mixins", "useLogin", "infos", "gwLists", "loading", "finished", "refreshing", "useQueryPage", "useFunCallApi", "body", "page", "pageSize", "total", "status", "firmManagementId", "methods", "goBack", "uni", "goTJ", "url", "content", "success", "goDetail", "onShow", "onLoad"], "mappings": "6JAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,yCACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,wHAITC,QAAS,WACP,OAAO,sHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,MAAMP,EAAIQ,UAAU,SAAUC,EAAMC,GAC/C,IAAIC,EAAQX,EAAIY,WAAWH,GACvBI,EAAKC,OAAOC,OAAO,GAAIN,EAAM,CAAEC,IAAKA,IACxC,MAAO,CACLC,MAAOA,EACPE,GAAIA,OAGJG,EAAKhB,EAAIQ,SAASS,OACtBjB,EAAIkB,IAAIC,KAAOL,OAAOC,OACpB,GACA,CACEK,MAAO,CACLhB,GAAIA,EACJY,GAAIA,MAKRK,GAAmB,EACnBC,EAAkB,GACtBvB,EAAOwB,eAAgB,G,iCCxDvB,yHAAkwB,eAAG,G,2HCyCrwB,YAIA,W,4HAEA,CACAC,iBACAjC,YACAkC,gBAEAC,mBACAP,gBACA,OACAQ,YACAC,SACAC,WACAC,WACAC,YACAC,cACAC,gBACAC,0BAAA,iCACAC,MACAC,OACAC,YACAC,QACAC,SACAC,uBAIAC,SACAC,kBACAC,kBAEAC,gBACA,cACAD,cACAE,mNAIAF,aACAG,0BACAC,oBACAlD,qBACA,UAEA8C,aACAE,6BAOAG,qBACAnD,yBACA8C,cACAE,sRAIAI,kBACA,iCACA,gCACA,mBAEAC,mBAAA,WACA,OACA,6BACA,IACA,2CACArD,oBAEA,aAGA,mBAEA,c,6DCzHA,yHAA6kC,eAAG,G", "file": "bussinessPages/pldDetail/pldDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './bussinessPages/pldDetail/pldDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./pldDetail.vue?vue&type=template&id=c1d9b78e&\"\nvar renderjs\nimport script from \"./pldDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./pldDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./pldDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/pldDetail/pldDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pldDetail.vue?vue&type=template&id=c1d9b78e&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    zPaging: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/z-paging/components/z-paging/z-paging\" */ \"@/uni_modules/z-paging/components/z-paging/z-paging.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.dataList, function (cell, idx) {\n    var $orig = _vm.__get_orig(cell)\n    var a0 = Object.assign({}, cell, { idx: idx })\n    return {\n      $orig: $orig,\n      a0: a0,\n    }\n  })\n  var g0 = _vm.dataList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pldDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pldDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"detail-container\">\r\n\t\t<!-- 顶部公司信息区域 -->\r\n\t\t<view class=\"company-header\">\r\n\t\t\t<!-- <image @click=\"goBack\" src=\"../rsjDir/bac-icon.png\" class=\"bac-icon\" /> -->\r\n\t\t\t<view class=\"company-basic-info\">\r\n\t\t\t\t<view class=\"company-name\">{{ infos.firmName }}</view>\r\n\t\t\t\t<view class=\"company-address\">\r\n\t\t\t\t\t<uni-icons type=\"location\" color=\"#fff\" size=\"14\" />\r\n\t\t\t\t\t<text>{{ infos.firmAddress }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"company-phone\">\r\n\t\t\t\t\t<uni-icons type=\"phone\" color=\"#fff\" size=\"14\" />\r\n\t\t\t\t\t<text>{{ infos.contactTel }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 公司介绍 -->\r\n\t\t<view class=\"company-intro\">\r\n\t\t\t<view class=\"intro-title\">公司介绍</view>\r\n\t\t\t<view class=\"intro-list\">\r\n\t\t\t\t<view class=\"intro-item\">\r\n\t\t\t\t\t<rich-text :nodes=\"infos.firmAbstract\"></rich-text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"intro-title\">工作环境</view>\r\n\t\t\t<view class=\"intro-list\">\r\n\t\t\t\t<view class=\"intro-item\">\r\n\t\t\t\t\t<rich-text :nodes=\"infos.firmEnv\"></rich-text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<z-paging :auto=\"false\" :fixed=\"false\" height=\"50vh\" ref=\"paging\" @query=\"queryList\"\r\n\t\t\t\t@onRefresh=\"onRefresh\">\r\n\t\t\t\t<leidaCellVue v-for=\"(cell,idx) in dataList\" :key=\"idx\" :obj=\"{...cell,idx}\" @click=\"goDetail\" />\r\n\t\t\t</z-paging>\r\n\t\t</view>\r\n\t\t<button v-if=\"dataList.length\" class=\"fixed-btn\" @click=\"goTJ\">提交意向</button>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCompanyDetail,\r\n\t\tgetPositionLists\r\n\t} from '@/api/rs.js'\r\n\timport table from '../../mixins/table';\r\n\timport leidaCellVue from './leidaCell.vue'\r\n\texport default {\r\n\t\tname: 'pLdDetail',\r\n\t\tcomponents: {\r\n\t\t\tleidaCellVue\r\n\t\t},\r\n\t\tmixins: [table],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuseLogin: false,\r\n\t\t\t\tinfos: {},\r\n\t\t\t\tgwLists: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\tfinished: true,\r\n\t\t\t\trefreshing: false,\r\n\t\t\t\tuseQueryPage: false,\r\n\t\t\t\tuseFunCallApi: (data) => getPositionLists(data),\r\n\t\t\t\tbody: {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tpageSize: 10,\r\n\t\t\t\t\ttotal: 0,\r\n\t\t\t\t\tstatus: 0,\r\n\t\t\t\t\tfirmManagementId: ''\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\tgoTJ() {\r\n\t\t\t\tif (this.useLogin) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/bussinessPages/tjForm/tjForm?cName=${this.infos.firmName}&companyId=${this.body.firmManagementId}&firmStreet=${this.infos.firmStreet}&cAddress=${this.infos.firmAddress}`\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\telse {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\tcontent: \"暂未登录，请先登录后再进行操作\",\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\tconsole.log(\"r3s\", res)\r\n\t\t\t\t\t\t\tif (res.cancel) {\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/mine/mine'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tconsole.log(\"111111 \", item)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/bussinessPages/gwDetail/gwDetail?id=${item.id}&companyId=${this.body.firmManagementId}&cName=${this.infos.firmName}&firmStreet=${this.infos.firmStreet}&cAddress=${this.infos.firmAddress}&cTel=${this.infos.firmLegalTel}`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\t\t\tconst userToken = uni.getStorageSync('userToken')\r\n\t\t\tthis.useLogin = userToken ? true : false\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tlet id = options.id\r\n\t\t\tthis.body.firmManagementId = id\r\n\t\t\tif (id) {\r\n\t\t\t\tgetCompanyDetail(id).then(res => {\r\n\t\t\t\t\tconsole.log(\"详情\", res)\r\n\t\t\t\t\t// 企业详情\r\n\t\t\t\t\tthis.infos = res\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\tthis.fetchList()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.detail-container {\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tmin-height: 70vh;\r\n\t\t/* padding-bottom: 80px; */\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.fixed-btn {\r\n\t\tposition: fixed;\r\n\t\tbottom: 5%;\r\n\t\tleft: 5%;\r\n\t\twidth: 90%;\r\n\t\tbackground-color: #4A89DC;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\t.bac-icon {\r\n\t\twidth: 34px;\r\n\t\theight: 34px;\r\n\t}\r\n\r\n\t.company-header {\r\n\t\tbackground-color: #4a89dc;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 20px 15px 30px;\r\n\t\tborder-radius: 0 0 20px 20px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.back-icon {\r\n\t\tposition: absolute;\r\n\t\ttop: 10px;\r\n\t\tleft: 0px;\r\n\t}\r\n\r\n\t.company-basic-info {\r\n\t\tpadding-top: 20px;\r\n\t}\r\n\r\n\t.company-name {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.company-address,\r\n\t.company-phone {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 14px;\r\n\t\tmargin-bottom: 5px;\r\n\t}\r\n\r\n\t.company-address .uni-icons,\r\n\t.company-phone .uni-icons {\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.job-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10px;\r\n\t\tmargin: 15px;\r\n\t\tpadding: 15px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.job-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.job-desc {\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.job-item,\r\n\t.intro-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 5px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.item-num {\r\n\t\tmargin-right: 5px;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.job-salary {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.salary-amount {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #f56c6c;\r\n\t}\r\n\r\n\t.salary-unit {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.company-intro {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10px;\r\n\t\tmargin: 15px;\r\n\t\tpadding: 15px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.intro-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pldDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./pldDetail.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}