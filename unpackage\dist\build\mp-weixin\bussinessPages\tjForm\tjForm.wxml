<view class="form-body data-v-2d081467" style="padding:0;"><view class="form-content data-v-2d081467"><uni-forms vue-id="157c4761-1" validateTrigger="bind" modelValue="{{formData}}" label-position="top" label-width="200" data-ref="formVal" class="data-v-2d081467 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('157c4761-2')+','+('157c4761-1')}}" label="公司名称" name="firmManagementName" class="data-v-2d081467" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('157c4761-3')+','+('157c4761-2')}}" maxlength="100" type="textarea" placeholder="请输入公司名称" value="{{formData.firmManagementName}}" data-event-opts="{{[['^input',[['__set_model',['$0','firmManagementName','$event',[]],['formData']]]]]}}" class="data-v-2d081467" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('157c4761-4')+','+('157c4761-1')}}" label="求职意向" name="firmManagementPostId" required="{{true}}" class="data-v-2d081467" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-select vue-id="{{('157c4761-5')+','+('157c4761-4')}}" localdata="{{jobList}}" value="{{formData.firmManagementPostId}}" data-event-opts="{{[['^change',[['postChange']]],['^input',[['__set_model',['$0','firmManagementPostId','$event',[]],['formData']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-2d081467" bind:__l="__l"></uni-data-select></uni-forms-item><uni-forms-item vue-id="{{('157c4761-6')+','+('157c4761-1')}}" label="您的姓名" name="intentionPersonName" required="{{true}}" class="data-v-2d081467" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('157c4761-7')+','+('157c4761-6')}}" type="text" maxLength="100" placeholder="请输入姓名" value="{{formData.intentionPersonName}}" data-event-opts="{{[['^input',[['__set_model',['$0','intentionPersonName','$event',[]],['formData']]]]]}}" class="data-v-2d081467" bind:__l="__l"></uni-easyinput><view class="float-tips-num data-v-2d081467"><text style="color:#CACACA;" class="data-v-2d081467">{{formData.intentionPersonName?$root.g0:0}}</text>/100</view></uni-forms-item><uni-forms-item vue-id="{{('157c4761-8')+','+('157c4761-1')}}" label="您的手机号" name="intentionPersonPhone" class="data-v-2d081467" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('157c4761-9')+','+('157c4761-8')}}" disabled="{{false}}" type="number" maxlength="11" placeholder="请输入联系人手机" value="{{formData.intentionPersonPhone}}" data-event-opts="{{[['^input',[['__set_model',['$0','intentionPersonPhone','$event',[]],['formData']]]]]}}" class="data-v-2d081467" bind:__l="__l"></uni-easyinput></uni-forms-item><uni-forms-item vue-id="{{('157c4761-10')+','+('157c4761-1')}}" label="您的简历" class="data-v-2d081467" bind:__l="__l" vue-slots="{{['default']}}"><uni-file-picker vue-id="{{('157c4761-11')+','+('157c4761-10')}}" file-mediatype="all" auto-upload="{{false}}" mode="grid" limit="{{1}}" data-ref="uploadFile" value="{{photo}}" data-event-opts="{{[['^progress',[['fileProgress']]],['^success',[['fileSuccess']]],['^fail',[['fileFail']]],['^select',[['fileSelectDiy']]],['^input',[['__set_model',['','photo','$event',[]]]]]]}}" bind:progress="__e" bind:success="__e" bind:fail="__e" bind:select="__e" bind:input="__e" class="data-v-2d081467 vue-ref" bind:__l="__l"></uni-file-picker><view style="color:#999999;" class="data-v-2d081467">注意: 图片、PDF、word附件，限制20M以内，限制一份</view></uni-forms-item></uni-forms></view><view data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="btn btn_primary form_btn data-v-2d081467" bindtap="__e">提交</view></view>