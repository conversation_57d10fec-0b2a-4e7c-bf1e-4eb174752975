{"pages": ["pages/index/index", "pages/mine/mine"], "subPackages": [{"root": "bussinessPages", "pages": ["webview/webview", "mapPage/mapPage", "policyPage/policyPage", "updateMine/updateMine", "tjForm/tjForm", "setting/setting", "leidaPage/leidaPage", "gwDetail/gwDetail", "qrCodePage/qrCodePage", "pldDetail/pldDetail", "tjRecordPages/tjRecordPages", "tjRecordDetail/tjRecordDetail"]}], "window": {"navigationBarTextStyle": "black", "navigationBarTitleText": "uni-app", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#7A8499", "selectedColor": "#0080FF", "borderStyle": "black", "backgroundColor": "#FFFFFF", "fontSize": "26upx", "iconWidth": "30px", "height": "60px", "list": [{"pagePath": "pages/index/index", "iconPath": "static/tabar/tab-0.png", "selectedIconPath": "static/tabar/tab-1.png", "text": "首页"}, {"pagePath": "pages/mine/mine", "iconPath": "static/tabar/tab-1-0.png", "selectedIconPath": "static/tabar/tab-1-1.png", "text": "我的"}]}, "networkTimeout": {"request": 60000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}, "permission": {"scope.userLocation": {"desc": "功能需求"}}, "usingComponents": {}}