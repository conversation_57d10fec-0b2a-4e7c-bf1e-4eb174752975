(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/mapPage/mapPage"],{72:function(e,t,n){"use strict";(function(e,t){var o=n(4);n(26);o(n(25));var r=o(n(73));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n(1)["default"],n(2)["createPage"])},73:function(e,t,n){"use strict";n.r(t);var o=n(74),r=n(76);for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(a);n(82);var c,i=n(32),s=Object(i["default"])(r["default"],o["render"],o["staticRenderFns"],!1,null,"2698651d",null,!1,o["components"],c);s.options.__file="bussinessPages/mapPage/mapPage.vue",t["default"]=s.exports},74:function(e,t,n){"use strict";n.r(t);var o=n(75);n.d(t,"render",(function(){return o["render"]})),n.d(t,"staticRenderFns",(function(){return o["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return o["recyclableRender"]})),n.d(t,"components",(function(){return o["components"]}))},75:function(e,t,n){"use strict";var o;n.r(t),n.d(t,"render",(function(){return r})),n.d(t,"staticRenderFns",(function(){return c})),n.d(t,"recyclableRender",(function(){return a})),n.d(t,"components",(function(){return o}));try{o={liuEasyMap:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map")]).then(n.bind(null,204))}}}catch(i){if(-1===i.message.indexOf("Cannot find module")||-1===i.message.indexOf(".vue"))throw i;console.error(i.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var r=function(){var e=this,t=e.$createElement;e._self._c},a=!1,c=[];r._withStripped=!0},76:function(e,t,n){"use strict";n.r(t);var o=n(77),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},77:function(e,t,n){"use strict";(function(e){var o=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n(37)),a=o(n(11)),c=o(n(39)),i=n(47),s=n(78);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f=function(){n.e("components/searchBar").then(function(){return resolve(n(217))}.bind(null,n)).catch(n.oe)},d={data:function(){return{markerData:[],polygons:[{points:[{latitude:"24.931423",longitude:"118.649744"}],strokeWidth:2,strokeColor:"#FF000060",fillColor:"#FF000090"}],closeIcon:n(79),markerImgIn:n(80),goImgIn:n(81),centerTar:{centerLat:"24.931423",centerLng:"118.649744"},body:{substreet:"",page:1,pageSize:10,total:0,status:0},streetLists:s.streetLists,finished:!1,isUseMyLocation:!1}},components:{searchBarVue:f},methods:{handlerAreaSelect:function(e){console.log("选11111111择区域",e.text),this.body.substreet="全部街道"==e.text?"":e.text;var t=this.streetLists[e.text];console.log("选择区域",e,t),this.streetPoint={latitude:t.latitude,centerLng:t.longitude},this.centerTar.centerLat=t.latitude,this.centerTar.centerLng=t.longitude,this.refresh()},handlerSearch:function(e){console.log("搜索",e),this.body.serviceName=e.keyword,this.refresh()},refresh:function(){this.markerData=[],this.finished=!1,this.body.page=1,this.fetchMapLists()},regionchange:function(){this.finished||this.fetchMapLists()},fetchMapLists:function(){var e=this;return(0,c.default)(r.default.mark((function t(){var n,o,a,c,s,l;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return null===(n=e.$refs)||void 0===n||null===(o=n.liuEasyMap)||void 0===o||o.clearMarker(),a=e.body,t.next=4,(0,i.getServiceLists)(a);case 4:c=t.sent,console.log("pageConfig",c),s=[],e.body.total=c.total,s=c.records,e.markerData=e.markerData.concat(s),(e.markerData.length>e.body.total||e.markerData.length===e.body.total)&&(e.finished=!0),e.markerData=e.markerData.map((function(e,t){return u(u({},e),{},{id:t,name:e.serviceName,address:e.serviceAddress,latitude:e.lat,longitude:e.lon,calloutColor:"#ffffff",calloutFontSize:14,calloutBorderRadius:6,calloutPadding:8,calloutBgColor:"#0B6CFF",calloutDisplay:"ALWAYS"})})),e.markerData&&(l=e.markerData.filter((function(e){return e.latitude})),e.isUseMyLocation||(e.centerTar={centerLat:l.length?l[0].latitude:e.streetPoint.latitude,centerLng:l.length?l[0].longitude:e.streetPoint.centerLng})),console.log("mockLats",e.markerData);case 15:case"end":return t.stop()}}),t)})))()},markerClick:function(e){console.log("点击标记点信息1：",e)},getLocationCurrent:function(){var e=this;if(console.log("_.dataList",e.markerData),e.markerData.length){var t=e.markerData[0];t.placeLatitude?(e.centerTar.centerLat=t.placeLatitude,e.centerTar.centerLng=t.placeLongitude):(console.log("7666666666"),this.getLocation())}else console.log("7666666666"),this.getLocation()},getWebLocation:function(){var t=this;e.getLocation({type:"wgs84",success:function(e){console.log("当前位置的经度：",e),t.centerTar.centerLat=e.latitude,t.centerTar.centerLng=e.longitude,this.isUseMyLocation=!0},fail:function(e){console.log("错误：",e),this.isUseMyLocation=!1}})},getLocation:function(){var t=this,n=this;e.getLocation({type:"wgs84",success:function(e){console.log("当前位置的经度："+e.longitude),console.log("当前位置的纬度："+e.latitude),n.centerTar.centerLng=e.longitude,n.centerTar.centerLat=e.latitude,t.isUseMyLocation=!0,console.log(e,"根据经纬度获取地址")},fail:function(n){e.showModal({content:"检测到您没打开获取位置功能权限，是否去设置打开？",confirmText:"确认",cancelText:"取消",success:function(n){n.confirm?e.openSetting({success:function(t){e.showToast({title:"授权后请重新打开此页面",icon:"none"})},fail:function(e){console.log(e)}}):e.showToast({title:"获取地理位置授权失败",icon:"none",success:function(){t.isUseMyLocation=!1}})}})}})}},onLoad:function(){this.refresh()},onShow:function(){this.getLocationCurrent()}};t.default=d}).call(this,n(2)["default"])},82:function(e,t,n){"use strict";n.r(t);var o=n(83),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},83:function(e,t,n){}},[[72,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/bussinessPages/mapPage/mapPage.js.map