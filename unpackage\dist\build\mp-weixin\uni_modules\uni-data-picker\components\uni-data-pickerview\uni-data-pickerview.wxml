<view class="uni-data-pickerview"><block wx:if="{{!isCloudDataList}}"><scroll-view class="selected-area" scroll-x="true"><view class="selected-list"><block wx:for="{{selected}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['handleSelect',[index]]]]]}}" class="{{['selected-item',(index==selectedIndex)?'selected-item-active':'']}}" bindtap="__e"><text>{{item.text||''}}</text></view></block></view></scroll-view></block><view class="tab-c"><scroll-view class="list" scroll-y="{{true}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="j" wx:key="j"><view data-event-opts="{{[['tap',[['handleNodeClick',['$0','$1',j],[[['dataList.'+selectedIndex+'','',j]],'selectedIndex']]]]]}}" class="{{['item',(!!item.$orig.disable)?'is-disabled':'']}}" bindtap="__e"><text class="item-text">{{item.$orig[map.text]}}</text><block wx:if="{{item.g0}}"><view class="check"></view></block></view></block></scroll-view><block wx:if="{{loading}}"><view class="loading-cover"><uni-load-more class="load-more" vue-id="d6e76fc8-1" contentText="{{loadMore}}" status="loading" bind:__l="__l"></uni-load-more></view></block><block wx:if="{{errorMessage}}"><view class="error-message"><text class="error-text">{{errorMessage}}</text></view></block></view></view>