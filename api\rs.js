import http from "../utils/request"
import httpSj from "../utils/requestSj.js"

// 政策列表
export function getPolicyLists({
	page,
	pageSize
}) {
	return httpSj.post(`/api/third/common/policyFile/${page}/${pageSize}`, {
		areaCodeList: ['350503'],
		isShow: 1,
		state: 1
	})
}

// 服务站点列表
export function getServiceLists(data) {
	// /api/zero/platform/service/list
	return http.post(`/api/v1/foreign/service/getAServiceLocation`, {
		...data
	})
}

// 查询岗位
export function getPositionLists(data) {
	return http.post(`/api/zero/platform/post/list`, {
		...data,
		index: data.page,
		size: data.pageSize
	})
}

// 岗位详情
export function getPositionDetail(id) {
	return http.get(`/api/zero/platform/post/detail/${id}`)
}
// 查询岗位
export function getPositionServiceDetail(id) {
	return http.get(`/api/v1/foreign/service/detail/${id}`)
}
// 获取岗位列表下拉用 
export function getPostListNames(data) {
	return http.post(`/api/v1/foreign/service/getPostListNames`, data)
}
// 查询公司 
export function getCompanyLists(data) {
	// /api/zero/platform/firm/list
	return http.post(`/api/v1/foreign/service/employmentRadar`, {
		...data,
		index: data.page,
		size: data.pageSize,
		firmServiceObj: data.firmServiceObj ? [data.firmServiceObj] : [],
		postTag: data.postTag ? [data.postTag] : [],
		reverseLookup: data.postTag ? 1 : 0
	})
}

// 公司详情
export function getCompanyDetail(id) {
	return http.get(`/api/zero/platform/firm/detail/${id}`, {})
}

// 获取字典值
export function getDictionary(code) {
	return http.get(`/api/zero/platform/firm/getDicEntityByCode/${code}`, {})
}

// 微信登录相关
export function wxLoginByCode(data) {
	return http.post(`/api/zero/wx/user/login/phone`, data)
}
// 退出登录 /api/zero/wx/user/logout
export function exitLogin(data) {
	return http.post(`/api/zero/wx/user/logout`, data)
}
// 获取用户信息 
export function getUserInfo(data) {
	return http.get(`/api/zero/wx/user/info`, data)
}

// 编辑用户信息 
export function editUser(data) {
	return http.post('/api/zero/wx/user/update', data)
}

// 获取级联组织架构 
export function getOrgData() {
	return http.get(`/api/v1/foreign/service/getOrgList`, {})
}
//  提交意向
export function addIntention(data){
	return http.post('/api/intentions/add', data)
}
// 获取提交意向列表 
export function getIntentionList(data){
	return http.post('/api/v1/foreign/service/list', data)
}
// 获取意向详情
export function getIntentionDetail(id){
	return http.get(`/api/v1/foreign/service/${id}`)
}