<template>
	<view class="area-container">
		<!-- 顶部搜索栏 -->
		<view class="search-header">
			<view class="search-input-box">
				<uni-search-bar style="width: 90%;" bgColor="#fff" v-model="body.firmName" placeholder="请输入搜索关键词"
					clearButton="auto" cancelButton="none" @confirm="search" @clear="onClear">
				</uni-search-bar>
			</view>
			<view class="search-btn" @click="search">搜索</view>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-tabs">
			<uni-data-select style="width: 32%" :clear="false" v-model="body.firmServiceObj" :localdata="option1"
				@change="onChangeAc"></uni-data-select>
			<uni-data-select style="width: 32%" :clear="false" v-model="body.firmStreet" :localdata="option2"
				@change="onChangeAc"></uni-data-select>
			<uni-data-select style="width: 32%" :clear="false" v-model="body.postTag" :localdata="option3"
				@change="onChangeAc"></uni-data-select>
		</view>

		<!-- 公司列表 -->
		<z-paging :auto="false" :fixed="false" height="77vh" ref="paging" @query="queryList" @onRefresh="onRefresh">
			<leidaCellVue v-for="(cell,idx) in dataList" :key="idx" :obj="{...cell,idx}" @onDetail="handlerDetail" />
		</z-paging>
	</view>
</template>

<script>
	import {
		getCompanyLists,
		getDictionary
	} from '../../api/rs.js'
	import table from '../../mixins/table';
	import leidaCellVue from './leidaCell.vue'
	export default {
		name: 'leidaPage',
		data() {
			return {
				api: '/api/zero/platform/firm/list',
				apiBase: '$http',
				useQueryPage: false,
				body: {
					firmServiceObj: '',
					firmStreet: '',
					postTag: '',
					firmName: '',
					firmStatus: 0,
					index: 1,
					size: 10,
					total: 0
				},
				useFunCallApi: (data) => getCompanyLists(data),
				option1: [],
				option2: [],
				option3: []
			}
		},
		mixins: [table],
		components: {
			leidaCellVue
		},
		onLoad() {
			this.fetchList()
			this.initDict()
		},
		methods: {
			handlerJump(item) {
				uni.navigateTo({
					url: '/pages/rsjPolicyLeidaDetail?id=' + item.id
				})
			},
			onClear() {
				this.body.firmName = ''
				this.onRestFetch()
			},
			onSearch() {
				// 实现搜索功能
				// console.log('搜索:', this.searchValue)
				this.onRefresh()
			},
			onChangeAc() {
				console.log("ffsdfsdfsdf")
				this.onRestFetch()
			},
			handlerDetail(item) {
				// const url = `https://ai.enzenith.com/job/#/rsjPolicyLeidaDetail`
				// const url = `https://aiv2.enzenith.com/job/#/rsjPolicyLeidaDetail` //测试
				// uni.navigateTo({
				// 	url: `/bussinessPages/webview/webview?url=${url}&id=${item.id}`,
				// })
				uni.navigateTo({
					url: `/bussinessPages/pldDetail/pldDetail?id=${item.id}`
				})
			},
			async initDict() {

				try {
					const xl = await getDictionary('serviceObject')
					const jd = await getDictionary('zeroStreet')
					const gw = await getDictionary('postTag')
					console.log('xl', xl)

					this.option1 = xl?.map(el => {
						return {
							text: el.entryName,
							value: el.entryCode
						}
					}) || []

					this.option2 = jd?.map(el => {
						return {
							text: el.entryName,
							value: el.entryName
						}
					}) || []
					
					this.option3 = gw?.map(el => {
						return {
							text: el.entryName,
							value: el.entryCode
						}
					}) || []
					this.option1.unshift({
						text: '全部服务对象',
						value: ''
					})
					this.option2.unshift({
						text: '全部街道',
						value: ''
					})
					console.log('jd', jd)
					this.option3.unshift({
						text: '全部岗位',
						value: ''
					})
					console.log('gw', gw)
				} catch (error) {
					console.error('获取字典数据失败', error)
				}
			},

		}
	}
</script>

<style scoped>
	.area-container {
		background-color: #f5f7fa;
		min-height: 95vh;
	}

	::v-deep .uni-select {
		border: none !important;
	}

	.search-btn {
		width: 88upx;
		height: 48upx;
		background: #4989FD;
		border-radius: 8upx;
		color: #fff;
		font-weight: 600;
		font-size: 28upx;
		text-align: center;
		line-height: 42upx;
		margin-right: 1%;
	}

	.bac-icon {
		width: 34px;
		height: 34px;
	}

	.search-header {
		position: sticky;
		top: 0;
		z-index: 100;
		background-color: #fff;
		box-sizing: border-box;
		padding: 12px 0 0;
		display: flex;
		align-items: center;
		justify-content: space-evenly;
	}

	.search-input-box {
		flex: 1;
	}

	.filter-tabs {
		background-color: #fff;
		margin-bottom: 10px;
		display: flex;
		justify-content: space-around;
		padding: 10px;
	}

	.company-list {
		padding: 0 10px;
	}

	.company-cell {
		margin-bottom: 10px;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	}

	.company-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 5px;
	}

	.company-info {
		display: flex;
		flex-direction: column;
		gap: 5px;
	}

	.info-item {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #666;
	}

	.info-item .uni-icons {
		margin-right: 5px;
	}

	.detail-link {
		color: #999;
		font-size: 14px;
	}

	.pulldown {
		height: calc(100vh - 120px);
	}
</style>