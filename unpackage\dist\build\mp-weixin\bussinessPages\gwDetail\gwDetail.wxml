<view class="detail-container"><view class="company-header"><view class="company-basic-info"><view class="company-name">{{cName||'-'}}</view><view class="company-address"><uni-icons vue-id="18a6787e-1" type="location" color="#fff" size="14" bind:__l="__l"></uni-icons><text>{{cAddress||'-'}}</text></view><view class="company-phone"><uni-icons vue-id="18a6787e-2" type="phone" color="#fff" size="14" bind:__l="__l"></uni-icons><text>{{cTel||'-'}}</text></view></view></view><view style="height:990rpx;overflow-y:auto;"><view class="job-card"><view class="job-title">{{infos.postName+"："+(infos.recruitingNumber||0)+"名"}}</view><view class="job-info"><view class="job-info-item"><text class="info-label">岗位类别：</text><text class="info-value">{{infos.postType||'-'}}</text></view><view class="job-info-item"><text class="info-label">福利待遇：</text><text class="info-value">{{infos.welfareTreatment||'-'}}</text></view><view class="job-info-item"><text class="info-label">工作地点：</text><text class="info-value">{{infos.workPlace||'-'}}</text></view><view class="job-info-item"><text class="info-label">工资：</text><text class="info-value">{{infos.salary||'-'}}</text></view><view class="job-info-item"><text class="info-label">有效期限：</text><text class="info-value">{{infos.validity?infos.validity+'天':'长期'}}</text></view></view></view><view class="job-requirements"><view class="section-title">岗位要求：</view><view class="requirement-list"><view class="requirement-item"><rich-text class="item-content" nodes="{{infos.postRequire}}"></rich-text></view></view></view><view class="work-environment"><view class="section-title">工作环境：</view><view class="environment-list"><rich-text class="item-content" nodes="{{infos.workEnv}}"></rich-text></view></view></view><button data-event-opts="{{[['tap',[['goTJ',['$event']]]]]}}" class="fixed-btn" bindtap="__e">提交意向</button></view>