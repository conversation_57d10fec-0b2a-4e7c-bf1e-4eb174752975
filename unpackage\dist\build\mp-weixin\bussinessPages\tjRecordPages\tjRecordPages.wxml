<view class="area-container data-v-1ab9ae3c"><view class="search-header data-v-1ab9ae3c"><view class="search-input-box data-v-1ab9ae3c"><uni-search-bar style="width:90%;" vue-id="b4537f36-1" bgColor="#fff" placeholder="请输入公司名称" clearButton="auto" cancelButton="none" value="{{body.firmManagementName}}" data-event-opts="{{[['^confirm',[['search']]],['^clear',[['onClear']]],['^input',[['__set_model',['$0','firmManagementName','$event',[]],['body']]]]]}}" bind:confirm="__e" bind:clear="__e" bind:input="__e" class="data-v-1ab9ae3c" bind:__l="__l"></uni-search-bar></view><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="search-btn data-v-1ab9ae3c" bindtap="__e">搜索</view></view><z-paging vue-id="b4537f36-2" auto="{{false}}" fixed="{{false}}" height="85vh" data-ref="paging" data-event-opts="{{[['^query',[['queryList']]],['^onRefresh',[['onRefresh']]]]}}" bind:query="__e" bind:onRefresh="__e" class="data-v-1ab9ae3c vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="cell" wx:for-index="idx" wx:key="idx"><leida-cell-vue vue-id="{{('b4537f36-3-'+idx)+','+('b4537f36-2')}}" obj="{{cell.a0}}" data-event-opts="{{[['^onDetail',[['handlerDetail']]]]}}" bind:onDetail="__e" class="data-v-1ab9ae3c" bind:__l="__l"></leida-cell-vue></block></z-paging></view>