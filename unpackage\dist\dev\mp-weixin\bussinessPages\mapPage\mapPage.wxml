<view class="map-box data-v-2698651d"><view class="data-v-2698651d"><search-bar-vue bind:onSelect="__e" bind:onSearch="__e" vue-id="a71680ee-1" data-event-opts="{{[['^onSelect',[['handlerAreaSelect']]],['^onSearch',[['handlerSearch']]]]}}" class="data-v-2698651d" bind:__l="__l"></search-bar-vue></view><liu-easy-map vue-id="a71680ee-2" centerLat="{{centerTar.centerLat}}" centerLng="{{centerTar.centerLng}}" scale="{{14}}" markerData="{{markerData}}" polygons="{{polygons}}" markerImgIn="{{markerImgIn}}" goImgIn="{{goImgIn}}" closeIcon="{{closeIcon}}" data-ref="liuEasyMap" data-event-opts="{{[['^clickMarker',[['markerClick']]],['^regionchange',[['regionchange']]]]}}" bind:clickMarker="__e" bind:regionchange="__e" class="data-v-2698651d vue-ref" bind:__l="__l"></liu-easy-map></view>