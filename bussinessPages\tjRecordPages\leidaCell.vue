<template>
	<view @click="$emit('onDetail', obj)" class="company-cell">
		<view class="header-cell">
			<view class="company-title over-text">{{ obj.firmManagementName }}</view>
			<view class="detail-link">详情 <uni-icons type="right" color="#999999" size="16"></uni-icons></view>
		</view>
		<view class="company-info">
<!-- 			<view class="info-item">
				<text class="over-text float-tit">公司名称：</text>
				<text class="over-text float-value">{{ obj.firmManagementName || '-' }}</text>
			</view> -->
			<view class="info-item">
				<text class="over-text float-tit">所属街道：</text>
				<text class="over-text float-value">{{ obj.firmStreet || '-' }}</text>
			</view>
			<view class="info-item">
				<text class="over-text float-tit">求职意向：</text>
				<text class="over-text float-value">{{ obj.firmManagementPostName || '-' }}</text>
			</view>
			<view class="info-item">
				<text class="over-text float-tit">提交时间：</text>
				<text class="over-text float-value">{{ obj.createTime || '-' }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "PolicyCell",
		props: {
			obj: {
				type: Object,
				default: () => {
					return {
						idx: 0,
						fileName: '',
						issuerStr: ''
					}
				}
			}
		},
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.company-cell {
		// margin-bottom: 10px;
		width: 95%;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
		padding: 24upx;
		box-sizing: border-box;
		margin: 20upx auto;
		background: #FFFFFF;
		height: auto;
	}

	::v-deep .uni-list-item__container[data-v-296a3d7e] {
		flex-direction: column !important;
	}

	.header-cell {
		width: 100%;
		display: flex;
		justify-content: space-between;
	}

	.float-tit {
		width: 150upx;
	}
	.float-value{
		width: calc(100% - 150upx)
	}
	.company-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 5px;
		width: 85%;
	}

	.company-info {
		display: flex;
		flex-direction: column;
		gap: 5px;
	}

	.info-item {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #666;
	}

	.info-item .uni-icons {
		margin-right: 5px;
	}

	.detail-link {
		color: #999;
		font-size: 14px;
		display: flex;
		align-items: center;
	}

	.pulldown {
		height: calc(100vh - 120px);
	}

	.over-text {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>