{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/qrCodePage/qrCodePage.vue?3ed6", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/qrCodePage/qrCodePage.vue?bd8f", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/qrCodePage/qrCodePage.vue?de8a", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/qrCodePage/qrCodePage.vue?63ad", "uni-app:///bussinessPages/qrCodePage/qrCodePage.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/qrCodePage/qrCodePage.vue?17e7"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "toggleQr", "toggleTit", "methods", "handleLongPress", "uni", "itemList", "success", "fail", "console", "shareToFriend", "title", "icon", "saveImageToPhone", "src", "filePath", "content", "confirmText", "collectImage", "onLoad"], "mappings": "+JAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,2CACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAAmwB,eAAG,G,sHCStwB,CACAC,gBACA,OACAC,YACAC,eAGAC,SAEAC,2BAAA,WAEAC,mBACAC,gCACAC,oBACA,mBACA,OACA,kBACA,MACA,OACA,qBACA,MACA,OACA,iBACA,QAGAC,iBACAC,0BAMAC,yBAGAL,aACAM,iBACAC,eAKAC,4BAEAR,gBACAS,kBACAP,oBAEAF,0BACAU,gBACAR,mBACAF,aACAM,aACAC,kBAGAJ,kBAEA,kCACAH,aACAM,WACAK,oBACAC,kBACAV,oBACA,WAEAF,mBAKAA,aACAM,aACAC,kBAMAJ,gBACAH,aACAM,iBACAC,kBAOAM,wBAEAb,aACAM,aACAC,mBAIAO,mBACA,qBACA,kDAEA,c,6DC/GA,yHAAsmC,eAAG,G", "file": "bussinessPages/qrCodePage/qrCodePage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './bussinessPages/qrCodePage/qrCodePage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./qrCodePage.vue?vue&type=template&id=c10c9c36&scoped=true&\"\nvar renderjs\nimport script from \"./qrCodePage.vue?vue&type=script&lang=js&\"\nexport * from \"./qrCodePage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./qrCodePage.vue?vue&type=style&index=0&id=c10c9c36&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"c10c9c36\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/qrCodePage/qrCodePage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrCodePage.vue?vue&type=template&id=c10c9c36&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrCodePage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrCodePage.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"content-box\">\r\n\t\t<view class=\"tit\">{{toggleTit}}</view>\r\n\t\t<image class=\"code-img\" :src=\"toggleQr\" @longpress=\"handleLongPress\"></image>\r\n\t\t<view>长按识别二维码</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttoggleQr: '',\r\n\t\t\t\ttoggleTit: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 处理长按事件\r\n\t\t\thandleLongPress() {\r\n\t\t\t\t// 调用微信小程序的API显示操作菜单\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: ['转发给朋友', '保存到手机', '收藏'],\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tswitch (res.tapIndex) {\r\n\t\t\t\t\t\t\tcase 0: // 转发给朋友\r\n\t\t\t\t\t\t\t\tthis.shareToFriend();\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tcase 1: // 保存到手机\r\n\t\t\t\t\t\t\t\tthis.saveImageToPhone();\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t\tcase 2: // 收藏\r\n\t\t\t\t\t\t\t\tthis.collectImage();\r\n\t\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\tconsole.log('操作取消', err);\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 转发给朋友\r\n\t\t\tshareToFriend() {\r\n\t\t\t\t// 在小程序中，可以使用button的open-type=\"share\"来实现分享\r\n\t\t\t\t// 这里只是模拟该功能\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '请点击右上角分享',\r\n\t\t\t\t\ticon: 'none'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 保存图片到手机\r\n\t\t\tsaveImageToPhone() {\r\n\t\t\t\t// 获取图片信息\r\n\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\tsrc: this.toggleQr,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t// 保存图片到相册\r\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\tfilePath: res.path,\r\n\t\t\t\t\t\t\tsuccess: () => {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '保存成功',\r\n\t\t\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\tfail: (err) => {\r\n\t\t\t\t\t\t\t\t// 如果失败可能是因为用户拒绝了授权\r\n\t\t\t\t\t\t\t\tif (err.errMsg.indexOf('auth deny') !== -1) {\r\n\t\t\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '提示',\r\n\t\t\t\t\t\t\t\t\t\tcontent: '需要您授权保存相册',\r\n\t\t\t\t\t\t\t\t\t\tconfirmText: '去设置',\r\n\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\t\t\t// 打开设置页面\r\n\t\t\t\t\t\t\t\t\t\t\t\tuni.openSetting();\r\n\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\t\ttitle: '保存失败',\r\n\t\t\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: () => {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '获取图片信息失败',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 收藏图片\r\n\t\t\tcollectImage() {\r\n\t\t\t\t// 这里可以实现收藏功能，例如保存到本地存储或发送到服务器\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '收藏成功',\r\n\t\t\t\t\ticon: 'success'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tthis.toggleTit = options.tit\r\n\t\t\tthis.toggleQr = require(`../../static/images/${options.idx}.jpg`)\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.content-box {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 30upx\r\n\t}\r\n\r\n\t.tit {\r\n\t\twidth: 100%;\r\n\t\ttext-align: left\r\n\t}\r\n\r\n\t.code-img {\r\n\t\twidth: 420upx;\r\n\t\theight: 420upx;\r\n\t\tobject-fit: contain;\r\n\t\tborder-radius: 16upx;\r\n\t\tbox-shadow: 0 0 16upx #e8e8e8;\r\n\t\tmargin-top: 100upx;\r\n\t\t/* background: orange; */\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrCodePage.vue?vue&type=style&index=0&id=c10c9c36&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./qrCodePage.vue?vue&type=style&index=0&id=c10c9c36&scoped=true&lang=css&\""], "sourceRoot": ""}