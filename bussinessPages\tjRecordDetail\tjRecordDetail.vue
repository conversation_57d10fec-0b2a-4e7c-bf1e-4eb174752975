<template>
	<view class="detail-container">
		<!-- 顶部公司信息区域 -->
		<view class="company-header">
			<!-- <image @click="goBack" src="../rsjDir/bac-icon.png" class="bac-icon" /> -->
			<view class="company-basic-info">
				<view class="company-name">{{ infos.firmManagementName }}</view>
				<view class="company-address">
					<uni-icons type="location" color="#fff" size="14" />
					<text>{{ com.firmAddress }}</text>
				</view>
				<view class="company-phone">
					<uni-icons type="phone" color="#fff" size="14" />
					<text>{{ com.contactTel }}</text>
					<view class="float-detail" @click="jumpDetailCompany">详情 <uni-icons type="right" color="#fff"
							size="14"></uni-icons></view>
				</view>
			</view>
		</view>
		<!-- 公司介绍 -->
		<view class="company-intro">
			<view class="job-info">
				<view class="job-info-item">
					<text class="info-label">求职意向：</text>
					<text class="info-value">{{infos.firmManagementPostName || '-'}}</text>
				</view>
				<view class="job-info-item">
					<text class="info-label">您的姓名：</text>
					<text class="info-value">{{infos.intentionPersonName || '-'}}</text>
				</view>
				<view class="job-info-item">
					<text class="info-label">您的手机号：</text>
					<text class="info-value">{{infos.intentionPersonPhone || '-'}}</text>
				</view>
				<view class="job-info-item">
					<text class="info-label">提交时间：</text>
					<text class="info-value">{{infos.createTime || '-'}}</text>
				</view>
				<view class="job-info-item">
					<text class="info-label">您的简历：</text>
					<text class="info-value" v-if="infos.intentionPersonFile" style="color:#4A89DC"
						@click="goDetailPolicy({filePath:infos.intentionPersonFile})">简历文件</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		getIntentionDetail,
		getCompanyDetail,
		getPositionLists
	} from '@/api/rs.js'
	import table from '../../mixins/table';
	import file from '../../mixins/file';
	export default {
		name: 'pLdDetail',
		components: {},
		mixins: [table, file],
		data() {
			return {
				useLogin: false,
				infos: {},
				gwLists: [],
				loading: false,
				finished: true,
				refreshing: false,
				useQueryPage: false,
				useFunCallApi: (data) => getPositionLists(data),
				body: {
					page: 1,
					pageSize: 10,
					total: 0,
					status: 0,
					firmManagementId: ''
				},
				com: {}
			}
		},
		methods: {
			isImageFile(file) {
				if (file) {
					const {
						filePath
					} =
					file
					const pattern = /\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/i;
					return pattern.test(filePath);
				}
			},
			goDetailPolicy(item) {
				if (this.isImageFile(item)){
					uni.previewImage({
						urls: [item.filePath]
					})
				}else this.handlerOpenPriviewFile(item)
			},
			goBack() {
				uni.navigateBack()
			},
			goTJ() {
				if (this.useLogin) {
					uni.navigateTo({
						url: `/bussinessPages/tjForm/tjForm?cName=${this.infos.firmName}&companyId=${this.body.firmManagementId}`
					})
				} else {
					uni.showModal({
						content: "暂未登录，请先登录后再进行操作",
						success(res) {
							console.log("r3s", res)
							if (res.cancel) {} else {
								uni.switchTab({
									url: '/pages/mine/mine'
								})
							}
						}
					})
				}
			},
			goDetail(item) {
				console.log("111111 ", item)
				uni.navigateTo({
					url: `/bussinessPages/gwDetail/gwDetail?id=${item.id}&companyId=${this.body.firmManagementId}&cName=${this.infos.firmName}&cAddress=${this.infos.firmAddress}&cTel=${this.infos.firmLegalTel}`
				})
			},
			jumpDetailCompany() {
				uni.navigateTo({
					url: `/bussinessPages/pldDetail/pldDetail?id=${this.com.id}`
				})
			}
		},
		onShow() {
			const userInfo = uni.getStorageSync('userInfo');
			const userToken = uni.getStorageSync('userToken')
			this.useLogin = userToken ? true : false
		},
		onLoad(options) {
			let id = options.id
			if (id) {
				getIntentionDetail(id).then(async res => {
					console.log("详情", res)
					// 企业详情
					this.infos = res
					const companyInfo = await getCompanyDetail(res.firmManagementId)
					console.log("gons详情", companyInfo)
					this.com = companyInfo
				})
			}
		}
	}
</script>

<style>
	.detail-container {
		background-color: #f5f7fa;
		min-height: 70vh;
		/* padding-bottom: 80px; */
		position: relative;
	}

	.fixed-btn {
		position: fixed;
		bottom: 5%;
		left: 5%;
		width: 90%;
		background-color: #4A89DC;
		color: #fff;
		border-radius: 50rpx;
	}

	.bac-icon {
		width: 34px;
		height: 34px;
	}

	.company-header {
		background-color: #4a89dc;
		color: #fff;
		padding: 20px 15px 30px;
		border-radius: 0 0 20px 20px;
		position: relative;
	}

	.back-icon {
		position: absolute;
		top: 10px;
		left: 0px;
	}

	.float-detail {
		font-size: 28upx;
		position: absolute;
		right: 3%;
	}

	.company-basic-info {
		padding-top: 20px;
	}

	.company-name {
		font-size: 18px;
		font-weight: bold;
		margin-bottom: 10px;
	}

	.company-address,
	.company-phone {
		display: flex;
		align-items: center;
		font-size: 14px;
		margin-bottom: 5px;
	}

	.company-address .uni-icons,
	.company-phone .uni-icons {
		margin-right: 5px;
	}

	.job-card {
		background-color: #fff;
		border-radius: 10px;
		margin: 15px;
		padding: 15px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	}

	.job-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
	}

	.job-desc {
		margin-bottom: 10px;
	}

	.job-item,
	.intro-item {
		display: flex;
		margin-bottom: 5px;
		font-size: 14px;
		color: #666;
	}

	.item-num {
		margin-right: 5px;
		flex-shrink: 0;
	}

	.job-info-item {
		display: flex;
		margin-bottom: 10px;
		font-size: 14px;
		color: #666;
	}

	.job-salary {
		text-align: right;
	}

	.info-label {
		flex-shrink: 0;
		color: #333;
		font-weight: 500;
	}

	.salary-amount {
		font-size: 18px;
		font-weight: bold;
		color: #f56c6c;
	}

	.salary-unit {
		font-size: 14px;
		color: #999;
	}

	.company-intro {
		background-color: #fff;
		border-radius: 10px;
		margin: 15px;
		padding: 15px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	}

	.intro-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
	}
</style>