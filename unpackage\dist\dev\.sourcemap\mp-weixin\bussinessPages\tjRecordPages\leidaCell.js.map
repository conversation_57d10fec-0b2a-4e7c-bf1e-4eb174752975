{"version": 3, "sources": ["webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordPages/leidaCell.vue?9b37", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordPages/leidaCell.vue?9161", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordPages/leidaCell.vue?79dc", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordPages/leidaCell.vue?1d8b", "uni-app:///bussinessPages/tjRecordPages/leidaCell.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordPages/leidaCell.vue?18dc"], "names": ["renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "name", "props", "obj", "type", "default", "idx", "fileName", "issuerStr", "data"], "mappings": "uIAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,6CACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCjCvB,yHAAkwB,eAAG,G,yGC4BrwB,CACAC,kBACAC,OACAC,KACAC,YACAC,mBACA,OACAC,MACAC,YACAC,iBAKAC,gBACA,WAIA,a,iCC/CA,yHAAq7C,eAAG,G", "file": "bussinessPages/tjRecordPages/leidaCell.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./leidaCell.vue?vue&type=template&id=33311db3&scoped=true&\"\nvar renderjs\nimport script from \"./leidaCell.vue?vue&type=script&lang=js&\"\nexport * from \"./leidaCell.vue?vue&type=script&lang=js&\"\nimport style0 from \"./leidaCell.vue?vue&type=style&index=0&id=33311db3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"33311db3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/tjRecordPages/leidaCell.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=template&id=33311db3&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view @click=\"$emit('onDetail', obj)\" class=\"company-cell\">\r\n\t\t<view class=\"header-cell\">\r\n\t\t\t<view class=\"company-title over-text\">{{ obj.firmManagementName }}</view>\r\n\t\t\t<view class=\"detail-link\">详情 <uni-icons type=\"right\" color=\"#999999\" size=\"16\"></uni-icons></view>\r\n\t\t</view>\r\n\t\t<view class=\"company-info\">\r\n<!-- \t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"over-text float-tit\">公司名称：</text>\r\n\t\t\t\t<text class=\"over-text float-value\">{{ obj.firmManagementName || '-' }}</text>\r\n\t\t\t</view> -->\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"over-text float-tit\">所属街道：</text>\r\n\t\t\t\t<text class=\"over-text float-value\">{{ obj.firmStreet || '-' }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"over-text float-tit\">求职意向：</text>\r\n\t\t\t\t<text class=\"over-text float-value\">{{ obj.firmManagementPostName || '-' }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"info-item\">\r\n\t\t\t\t<text class=\"over-text float-tit\">提交时间：</text>\r\n\t\t\t\t<text class=\"over-text float-value\">{{ obj.createTime || '-' }}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tname: \"PolicyCell\",\r\n\t\tprops: {\r\n\t\t\tobj: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault: () => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tidx: 0,\r\n\t\t\t\t\t\tfileName: '',\r\n\t\t\t\t\t\tissuerStr: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.company-cell {\r\n\t\t// margin-bottom: 10px;\r\n\t\twidth: 95%;\r\n\t\tborder-radius: 8px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);\r\n\t\tpadding: 24upx;\r\n\t\tbox-sizing: border-box;\r\n\t\tmargin: 20upx auto;\r\n\t\tbackground: #FFFFFF;\r\n\t\theight: auto;\r\n\t}\r\n\r\n\t::v-deep .uni-list-item__container[data-v-296a3d7e] {\r\n\t\tflex-direction: column !important;\r\n\t}\r\n\r\n\t.header-cell {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.float-tit {\r\n\t\twidth: 150upx;\r\n\t}\r\n\t.float-value{\r\n\t\twidth: calc(100% - 150upx)\r\n\t}\r\n\t.company-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 5px;\r\n\t\twidth: 85%;\r\n\t}\r\n\r\n\t.company-info {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 5px;\r\n\t}\r\n\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.info-item .uni-icons {\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.detail-link {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 14px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.pulldown {\r\n\t\theight: calc(100vh - 120px);\r\n\t}\r\n\r\n\t.over-text {\r\n\t\toverflow: hidden;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=style&index=0&id=33311db3&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=style&index=0&id=33311db3&lang=scss&scoped=true&\""], "sourceRoot": ""}