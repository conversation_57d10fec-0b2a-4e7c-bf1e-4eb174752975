<view class="uni-searchbar"><view data-event-opts="{{[['tap',[['searchClick',['$event']]]]]}}" class="uni-searchbar__box" style="{{'border-radius:'+(radius+'px')+';'+('background-color:'+(bgColor)+';')}}" bindtap="__e"><view class="uni-searchbar__box-icon-search"><block wx:if="{{$slots.searchIcon}}"><slot name="searchIcon"></slot></block><block wx:else><uni-icons vue-id="2caaf8be-1" color="#c0c4cc" size="18" type="search" bind:__l="__l"></uni-icons></block></view><block wx:if="{{show||searchVal}}"><input class="uni-searchbar__box-search-input" style="{{'color:'+(textColor)+';'}}" focus="{{showSync}}" disabled="{{readonly}}" placeholder="{{placeholderText}}" maxlength="{{maxlength}}" confirm-type="search" type="text" data-event-opts="{{[['confirm',[['confirm',['$event']]]],['blur',[['blur',['$event']]]],['focus',[['emitFocus',['$event']]]],['input',[['__set_model',['','searchVal','$event',[]]]]]]}}" value="{{searchVal}}" bindconfirm="__e" bindblur="__e" bindfocus="__e" bindinput="__e"/></block><block wx:else><text class="uni-searchbar__text-placeholder">{{placeholder}}</text></block><block wx:if="{{show&&(clearButton==='always'||clearButton==='auto'&&searchVal!=='')&&!readonly}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="uni-searchbar__box-icon-clear" bindtap="__e"><block wx:if="{{$slots.clearIcon}}"><slot name="clearIcon"></slot></block><block wx:else><uni-icons vue-id="2caaf8be-2" color="#c0c4cc" size="20" type="clear" bind:__l="__l"></uni-icons></block></view></block></view><block wx:if="{{cancelButton==='always'||show&&cancelButton==='auto'}}"><text data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="uni-searchbar__cancel" bindtap="__e">{{cancelTextI18n}}</text></block></view>