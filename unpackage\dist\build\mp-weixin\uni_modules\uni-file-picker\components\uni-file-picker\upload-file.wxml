<view class="uni-file-picker__files"><block wx:if="{{!readonly}}"><view data-event-opts="{{[['tap',[['choose',['$event']]]]]}}" class="files-button" bindtap="__e"><slot></slot></view></block><block wx:if="{{$root.g0>0}}"><view class="uni-file-picker__lists is-text-box" style="{{(borderStyle)}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['uni-file-picker__lists-box',(index!==0&&styles.dividline)?'files-border':'']}}" style="{{(index!==0&&styles.dividline&&borderLineStyle)}}"><view class="uni-file-picker__item"><view class="files__name">{{item.name}}</view><block wx:if="{{delIcon&&!readonly}}"><view data-event-opts="{{[['tap',[['delFile',[index]]]]]}}" class="icon-del-box icon-files" bindtap="__e"><view class="icon-del icon-files"></view><view class="icon-del rotate"></view></view></block></view><block wx:if="{{item.progress&&item.progress!==100||item.progress===0}}"><view class="file-picker__progress"><progress class="file-picker__progress-item" percent="{{item.progress===-1?0:item.progress}}" stroke-width="4" backgroundColor="{{item.errMsg?'#ff5a5f':'#EBEBEB'}}"></progress></view></block><block wx:if="{{item.status==='error'}}"><view data-event-opts="{{[['tap',[['uploadFiles',['$0',index],[[['list','',index]]]]]]]}}" class="file-picker__mask" catchtap="__e">点击重试</view></block></view></block></view></block></view>