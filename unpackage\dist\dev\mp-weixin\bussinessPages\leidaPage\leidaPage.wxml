<view class="area-container data-v-79c0e63f"><view class="search-header data-v-79c0e63f"><view class="search-input-box data-v-79c0e63f"><uni-search-bar style="width:90%;" vue-id="83473ab2-1" bgColor="#fff" placeholder="请输入搜索关键词" clearButton="auto" cancelButton="none" value="{{body.firmName}}" data-event-opts="{{[['^confirm',[['search']]],['^clear',[['onClear']]],['^input',[['__set_model',['$0','firmName','$event',[]],['body']]]]]}}" bind:confirm="__e" bind:clear="__e" bind:input="__e" class="data-v-79c0e63f" bind:__l="__l"></uni-search-bar></view><view data-event-opts="{{[['tap',[['search',['$event']]]]]}}" class="search-btn data-v-79c0e63f" bindtap="__e">搜索</view></view><view class="filter-tabs data-v-79c0e63f"><uni-data-select style="width:32%;" vue-id="83473ab2-2" clear="{{false}}" localdata="{{option1}}" value="{{body.firmServiceObj}}" data-event-opts="{{[['^change',[['onChangeAc']]],['^input',[['__set_model',['$0','firmServiceObj','$event',[]],['body']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-79c0e63f" bind:__l="__l"></uni-data-select><uni-data-select style="width:32%;" vue-id="83473ab2-3" clear="{{false}}" localdata="{{option2}}" value="{{body.firmStreet}}" data-event-opts="{{[['^change',[['onChangeAc']]],['^input',[['__set_model',['$0','firmStreet','$event',[]],['body']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-79c0e63f" bind:__l="__l"></uni-data-select><uni-data-select style="width:32%;" vue-id="83473ab2-4" clear="{{false}}" localdata="{{option3}}" value="{{body.postTag}}" data-event-opts="{{[['^change',[['onChangeAc']]],['^input',[['__set_model',['$0','postTag','$event',[]],['body']]]]]}}" bind:change="__e" bind:input="__e" class="data-v-79c0e63f" bind:__l="__l"></uni-data-select></view><z-paging vue-id="83473ab2-5" auto="{{false}}" fixed="{{false}}" height="77vh" data-ref="paging" data-event-opts="{{[['^query',[['queryList']]],['^onRefresh',[['onRefresh']]]]}}" bind:query="__e" bind:onRefresh="__e" class="data-v-79c0e63f vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="cell" wx:for-index="idx" wx:key="idx"><leida-cell-vue vue-id="{{('83473ab2-6-'+idx)+','+('83473ab2-5')}}" obj="{{cell.a0}}" data-event-opts="{{[['^onDetail',[['handlerDetail']]]]}}" bind:onDetail="__e" class="data-v-79c0e63f" bind:__l="__l"></leida-cell-vue></block></z-paging></view>