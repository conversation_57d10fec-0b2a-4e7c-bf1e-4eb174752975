(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/webview/webview"],{"2aa7":function(t,n,c){"use strict";c.r(n);var e=c("583a"),a=c("fd95");for(var o in a)["default"].indexOf(o)<0&&function(t){c.d(n,t,(function(){return a[t]}))}(o);var i=c("828b"),u=Object(i["a"])(a["default"],e["b"],e["c"],!1,null,null,null,!1,e["a"],void 0);n["default"]=u.exports},"3c80":function(t,n,c){"use strict";(function(t,n){var e=c("47a9");c("bfec");e(c("3240"));var a=e(c("2aa7"));t.__webpack_require_UNI_MP_PLUGIN__=c,n(a.default)}).call(this,c("3223")["default"],c("df3c")["createPage"])},"583a":function(t,n,c){"use strict";c.d(n,"b",(function(){return e})),c.d(n,"c",(function(){return a})),c.d(n,"a",(function(){}));var e=function(){var t=this.$createElement;this._self._c},a=[]},a4c0:function(t,n,c){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={data:function(){return{path:""}},onLoad:function(t){console.log("options",t),t.useCode?this.path="".concat(t.url,"?idx=").concat(t.idx,"&tit=").concat(t.tit):this.path=t.id?"".concat(null===t||void 0===t?void 0:t.url,"?id=").concat(t.id):"".concat(null===t||void 0===t?void 0:t.url,"?code=").concat(t.code)}};n.default=e},fd95:function(t,n,c){"use strict";c.r(n);var e=c("a4c0"),a=c.n(e);for(var o in e)["default"].indexOf(o)<0&&function(t){c.d(n,t,(function(){return e[t]}))}(o);n["default"]=a.a}},[["3c80","common/runtime","common/vendor"]]]);