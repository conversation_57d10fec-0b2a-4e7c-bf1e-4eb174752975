<view class="form-body data-v-2a04bef6" style="padding:0;"><view class="form-content data-v-2a04bef6"><uni-forms vue-id="74b6f121-1" validateTrigger="bind" modelValue="{{formData}}" label-position="top" label-width="200" data-ref="formVal" class="data-v-2a04bef6 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><uni-forms-item vue-id="{{('74b6f121-2')+','+('74b6f121-1')}}" label="头像" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-file-picker vue-id="{{('74b6f121-3')+','+('74b6f121-2')}}" file-mediatype="image" auto-upload="{{false}}" mode="grid" file-extname="png,jpg" limit="{{1}}" data-ref="uploadFile" value="{{photo}}" data-event-opts="{{[['^progress',[['fileProgress']]],['^success',[['fileSuccess']]],['^fail',[['fileFail']]],['^select',[['fileSelectDiy']]],['^input',[['__set_model',['','photo','$event',[]]]]]]}}" bind:progress="__e" bind:success="__e" bind:fail="__e" bind:select="__e" bind:input="__e" class="data-v-2a04bef6 vue-ref" bind:__l="__l"></uni-file-picker></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-4')+','+('74b6f121-1')}}" label="姓名" name="nickName" required="{{true}}" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('74b6f121-5')+','+('74b6f121-4')}}" type="text" maxLength="100" placeholder="请输入姓名" value="{{formData.nickName}}" data-event-opts="{{[['^input',[['__set_model',['$0','nickName','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-easyinput><view class="float-tips-num data-v-2a04bef6"><text style="color:#CACACA;" class="data-v-2a04bef6">{{formData.nickName?$root.g0:0}}</text>/100</view></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-6')+','+('74b6f121-1')}}" label="性别" name="sex" required="{{true}}" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-checkbox bind:input="__e" vue-id="{{('74b6f121-7')+','+('74b6f121-6')}}" localdata="{{dictSexOptions}}" value="{{formData.sex}}" data-event-opts="{{[['^input',[['__set_model',['$0','sex','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-data-checkbox></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-8')+','+('74b6f121-1')}}" label="出生年月" name="birthdate" required="{{true}}" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-datetime-picker bind:input="__e" vue-id="{{('74b6f121-9')+','+('74b6f121-8')}}" type="date" placeholder="请选择出生年月" value="{{formData.birthdate}}" data-event-opts="{{[['^input',[['__set_model',['$0','birthdate','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-datetime-picker></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-10')+','+('74b6f121-1')}}" label="居(租)住地" name="address" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-picker vue-id="{{('74b6f121-11')+','+('74b6f121-10')}}" localdata="{{addressLists}}" popup-title="请选择居(租)住地" value="{{formData.address}}" data-event-opts="{{[['^change',[['onchangeAddress']]],['^nodeclick',[['onnodeclick']]],['^input',[['__set_model',['$0','address','$event',[]],['formData']]]]]}}" bind:change="__e" bind:nodeclick="__e" bind:input="__e" class="data-v-2a04bef6" bind:__l="__l"></uni-data-picker></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-12')+','+('74b6f121-1')}}" label="详细地址" name="detailedAddress" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('74b6f121-13')+','+('74b6f121-12')}}" maxlength="100" type="textarea" placeholder="请输入详细地址" value="{{formData.detailedAddress}}" data-event-opts="{{[['^input',[['__set_model',['$0','detailedAddress','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-easyinput><view class="float-tips-num data-v-2a04bef6"><text style="color:#CACACA;" class="data-v-2a04bef6">{{formData.detailedAddress?$root.g1:0}}</text>/100</view></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-14')+','+('74b6f121-1')}}" label="求职意向" name="desiredField" required="{{true}}" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-select bind:input="__e" vue-id="{{('74b6f121-15')+','+('74b6f121-14')}}" localdata="{{jobList}}" value="{{formData.desiredField}}" data-event-opts="{{[['^input',[['__set_model',['$0','desiredField','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-data-select></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-16')+','+('74b6f121-1')}}" label="求职意向地" name="desiredLocationName" required="{{true}}" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-picker vue-id="{{('74b6f121-17')+','+('74b6f121-16')}}" localdata="{{addressLists}}" popup-title="请选择求职意向地" value="{{formData.desiredLocationName}}" data-event-opts="{{[['^change',[['onchangeDesiredLocation']]],['^nodeclick',[['onnodeclick']]],['^input',[['__set_model',['$0','desiredLocationName','$event',[]],['formData']]]]]}}" bind:change="__e" bind:nodeclick="__e" bind:input="__e" class="data-v-2a04bef6" bind:__l="__l"></uni-data-picker></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-18')+','+('74b6f121-1')}}" label="学历水平" name="educationLevel" required="{{true}}" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-data-select bind:input="__e" vue-id="{{('74b6f121-19')+','+('74b6f121-18')}}" localdata="{{educationalList}}" value="{{formData.educationLevel}}" data-event-opts="{{[['^input',[['__set_model',['$0','educationLevel','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-data-select></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-20')+','+('74b6f121-1')}}" label="毕业院校" name="graduationSchool" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('74b6f121-21')+','+('74b6f121-20')}}" maxlength="100" type="textarea" placeholder="请输入毕业院校" value="{{formData.graduationSchool}}" data-event-opts="{{[['^input',[['__set_model',['$0','graduationSchool','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-easyinput><view class="float-tips-num data-v-2a04bef6"><text style="color:#CACACA;" class="data-v-2a04bef6">{{formData.graduationSchool?$root.g2:0}}</text>/100</view></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-22')+','+('74b6f121-1')}}" label="技能水平" name="skillsLevel" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('74b6f121-23')+','+('74b6f121-22')}}" maxlength="500" type="textarea" placeholder="请输入技能水平" value="{{formData.skillsLevel}}" data-event-opts="{{[['^input',[['__set_model',['$0','skillsLevel','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-easyinput><view class="float-tips-num data-v-2a04bef6"><text style="color:#CACACA;" class="data-v-2a04bef6">{{formData.skillsLevel?$root.g3:0}}</text>/500</view></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-24')+','+('74b6f121-1')}}" label="工作经历" name="workExperience" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('74b6f121-25')+','+('74b6f121-24')}}" maxlength="500" type="textarea" placeholder="请输入工作经历" value="{{formData.workExperience}}" data-event-opts="{{[['^input',[['__set_model',['$0','workExperience','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-easyinput><view class="float-tips-num data-v-2a04bef6"><text style="color:#CACACA;" class="data-v-2a04bef6">{{formData.workExperience?$root.g4:0}}</text>/500</view></uni-forms-item><uni-forms-item vue-id="{{('74b6f121-26')+','+('74b6f121-1')}}" label="联系手机" name="phone" class="data-v-2a04bef6" bind:__l="__l" vue-slots="{{['default']}}"><uni-easyinput bind:input="__e" vue-id="{{('74b6f121-27')+','+('74b6f121-26')}}" disabled="{{true}}" type="number" maxlength="11" placeholder="请输入联系人手机" value="{{formData.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['formData']]]]]}}" class="data-v-2a04bef6" bind:__l="__l"></uni-easyinput></uni-forms-item></uni-forms></view><view data-event-opts="{{[['tap',[['submitForm',['$event']]]]]}}" class="btn btn_primary form_btn data-v-2a04bef6" bindtap="__e">提交</view></view>