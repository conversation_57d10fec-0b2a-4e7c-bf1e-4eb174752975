@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-load-more {
  display: flex;
  flex-direction: row;
  height: 40px;
  align-items: center;
  justify-content: center;
}
.uni-load-more__text {
  font-size: 14px;
  margin-left: 8px;
}
.uni-load-more__img {
  width: 24px;
  height: 24px;
}
.uni-load-more__img--nvue {
  color: #666666;
}
.uni-load-more__img--android,
.uni-load-more__img--ios {
  width: 24px;
  height: 24px;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
}
.uni-load-more__img--android {
  -webkit-animation: loading-ios 1s 0s linear infinite;
          animation: loading-ios 1s 0s linear infinite;
}
@-webkit-keyframes loading-android {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-android {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.uni-load-more__img--ios-H5 {
  position: relative;
  -webkit-animation: loading-ios-H5 1s 0s step-end infinite;
          animation: loading-ios-H5 1s 0s step-end infinite;
}
.uni-load-more__img--ios-H5 image {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
}
@-webkit-keyframes loading-ios-H5 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
8% {
    -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
}
16% {
    -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
}
24% {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
}
32% {
    -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
}
40% {
    -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
}
48% {
    -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
}
56% {
    -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
}
64% {
    -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
}
73% {
    -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
}
82% {
    -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
}
91% {
    -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-ios-H5 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
8% {
    -webkit-transform: rotate(30deg);
            transform: rotate(30deg);
}
16% {
    -webkit-transform: rotate(60deg);
            transform: rotate(60deg);
}
24% {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
}
32% {
    -webkit-transform: rotate(120deg);
            transform: rotate(120deg);
}
40% {
    -webkit-transform: rotate(150deg);
            transform: rotate(150deg);
}
48% {
    -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
}
56% {
    -webkit-transform: rotate(210deg);
            transform: rotate(210deg);
}
64% {
    -webkit-transform: rotate(240deg);
            transform: rotate(240deg);
}
73% {
    -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
}
82% {
    -webkit-transform: rotate(300deg);
            transform: rotate(300deg);
}
91% {
    -webkit-transform: rotate(330deg);
            transform: rotate(330deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
.uni-load-more__img--android-MP {
  position: relative;
  width: 24px;
  height: 24px;
  -webkit-transform: rotate(0deg);
          transform: rotate(0deg);
  -webkit-animation: loading-ios 1s 0s ease infinite;
          animation: loading-ios 1s 0s ease infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon {
  position: absolute;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: solid 2px transparent;
  border-top: solid 2px #777777;
  -webkit-transform-origin: center;
          transform-origin: center;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(1) {
  -webkit-animation: loading-android-MP-1 1s 0s linear infinite;
          animation: loading-android-MP-1 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(2) {
  -webkit-animation: loading-android-MP-2 1s 0s linear infinite;
          animation: loading-android-MP-2 1s 0s linear infinite;
}
.uni-load-more__img--android-MP .uni-load-more__img-icon:nth-child(3) {
  -webkit-animation: loading-android-MP-3 1s 0s linear infinite;
          animation: loading-android-MP-3 1s 0s linear infinite;
}
@keyframes loading-android {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@-webkit-keyframes loading-android-MP-1 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-android-MP-1 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(90deg);
            transform: rotate(90deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@-webkit-keyframes loading-android-MP-2 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-android-MP-2 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(180deg);
            transform: rotate(180deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@-webkit-keyframes loading-android-MP-3 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
@keyframes loading-android-MP-3 {
0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
50% {
    -webkit-transform: rotate(270deg);
            transform: rotate(270deg);
}
100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
}
}
