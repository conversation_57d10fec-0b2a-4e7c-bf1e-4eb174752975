<!--提交意向-->
<template>
	<view class="form-body" style="padding: 0;">
		<view class="form-content">
			<uni-forms validateTrigger='bind' :modelValue="formData" ref="formVal" label-position="top"
				label-width="200">
				<uni-forms-item label="公司名称" name="firmManagementName">
					<uni-easyinput maxlength="100" type="textarea" v-model="formData.firmManagementName"
						placeholder="请输入公司名称" />
					<!-- 					<view class="float-tips-num"><text
							style="color: #CACACA;">{{formData.firmManagementName ? formData.firmManagementName.length: 0}}</text>/100
					</view> -->
				</uni-forms-item>
				<uni-forms-item label="求职意向" name="firmManagementPostId" required>
					<uni-data-select v-model="formData.firmManagementPostId" :localdata="jobList"
						@change="postChange"></uni-data-select>
				</uni-forms-item>
				<uni-forms-item label="您的姓名" name="intentionPersonName" required>
					<uni-easyinput type="text" maxLength="100" v-model="formData.intentionPersonName"
						placeholder="请输入姓名" />
					<view class="float-tips-num"><text
							style="color: #CACACA;">{{formData.intentionPersonName ? formData.intentionPersonName.length: 0}}</text>/100
					</view>
				</uni-forms-item>
				<uni-forms-item label="您的手机号" name="intentionPersonPhone">
					<uni-easyinput :disabled="false" type="number" maxlength="11"
						v-model="formData.intentionPersonPhone" placeholder="请输入联系人手机" />
				</uni-forms-item>
				<uni-forms-item label="您的简历">
					<uni-file-picker ref="uploadFile" v-model="photo" file-mediatype="all" :auto-upload="false"
						mode="grid" :limit="1" @progress="fileProgress" @success="fileSuccess" @fail="fileFail"
						@select="fileSelectDiy" />
					<view style="color:#999999">注意: 图片、PDF、word附件，限制20M以内，限制一份</view>
				</uni-forms-item>
			</uni-forms>
		</view>
		<view class="btn btn_primary form_btn" @click="submitForm">提交</view>
	</view>
</template>

<script>
	let config = require('../../utils/' + process.env.NODE_ENV)
	import {
		mapGetters
	} from 'vuex'
	import {
		addIntention,
		getPostListNames
	} from "@/api/rs"
	export default {
		data() {
			// 测试身份证号
			function validateIdCard(rule, idCard, data, cb) {
				const idCardRegex = /^(?:\d{15}|\d{17}[\dXx])$/;
				if (!idCardRegex.test(idCard)) {
					cb("请输入正确的证件号");
				} else return true;
			}
			// 测试邮箱
			function validateEmail(rule, email, data, cb) {
				const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
				if (email.length && !emailRegex.test(email)) {
					cb("请输入正确的邮箱");
				} else return true;
			}
			return {
				formData: {
					firmManagementName: '',
					firmManagementPostId: '',
					firmManagementPostName: '',
					intentionPersonName: '',
					intentionPersonPhone: '',
					intentionPersonFile: '',
					firmStreet: ''
				},
				userInfo: null,
				companyId: '',
				photo: [],
				items: [{
						text: "一年级",
						value: "1-0",
						children: [{
								text: "1.1班",
								value: "1-1"
							},
							{
								text: "1.2班",
								value: "1-2"
							}
						]
					},
					{
						text: "二年级",
						value: "2-0"
					},
					{
						text: "三年级",
						value: "3-0"
					}
				],
				jobList: [], //求职意向
				rules: {
					intentionPersonName: {
						rules: [{
								required: true,
								errorMessage: '请输入姓名',
							},
							{
								maxLength: 100,
								errorMessage: '姓名长度最长 {maxLength} 个字符',
							}
						]
					},
					sex: {
						rules: [{
							required: true,
							errorMessage: '请选择性别',
						}]
					},
					birthdate: {
						rules: [{
							required: true,
							errorMessage: '请选择出生年月日',
						}]
					},
					firmManagementPostId: {
						rules: [{
							required: true,
							errorMessage: '请选择求职意向',
						}]
					},
					educationLevel: {
						rules: [{
							required: true,
							errorMessage: '请选择学历水平',
						}]
					},
					desiredField: {
						rules: [{
							required: true,
							errorMessage: '请选择求职意向',
						}]
					},
				}
			};
		},
		computed: {

		},
		watch: {
			formData: {
				handler(val) {},
				deep: true,
				immediate: true,
			},
		},
		methods: {
			async initDict() {
				const jobList = await getPostListNames({
					firmManagementId: this.companyId,
					status: 1
				})
				console.log("jobList", jobList)
				this.jobList = jobList?.map(el => {
					return {
						value: el.id,
						text: el.postName
					}
				})
				if(this.formData.firmManagementPostId) { this.postChange(this.formData.firmManagementPostId) }
			},
			postChange(e) {
				let tar = this.jobList.filter(el => el.value == e)
				console.log("postChange", tar)
				this.formData.firmManagementPostName = tar.length ? tar[0].text : ''
			},
			formatTreeData(treeData, textField = 'name', valueField = 'id', childrenField = 'children') {
				if (!treeData || !Array.isArray(treeData)) {
					return [];
				}

				return treeData.map(node => {
					// 创建新节点，避免修改原始数据
					const newNode = {
						...node,
						text: node[textField] || '',
						value: node[valueField] || ''
					};

					// 如果有子节点，递归处理
					if (node[childrenField] && Array.isArray(node[childrenField])) {
						newNode[childrenField] = this.formatTreeData(
							node[childrenField],
							textField,
							valueField,
							childrenField
						);
					}

					return newNode;
				});
			},
			onnodeclick(node) {},
			submitForm() {
				const {
					formData
				} = this
				console.log("formData", formData)
				this.$refs.formVal.validate().then(async res => {
					console.log('表单数据信息：', res);
					const putRes = await addIntention({
						...formData,
						firmManagementId: this.companyId,
						wxUserId: this.userInfo.userId
					})
					console.log("修改用户信息", putRes)
					if (putRes) {
						uni.showToast({
							title: "提交成功",
							icon: 'success'
						})
						setTimeout(() => {
							uni.navigateBack()
						}, 500);
					}
				}).catch(err => {
					console.log('表单错误信息：', err);
				})
			},
			// 上传文件
			async fileSelectDiy({
				tempFiles,
				tempFilePaths
			}) {
				const {
					baseApi
				} = config
				const actionUrl = `${baseApi}/api/v1/common/uploadAvatar`;
				console.log("diy", tempFiles, tempFilePaths, config, actionUrl)
				if (tempFiles[0].size > 20 * 1024 * 1024) {
					uni.showToast({
						title: "文件大小不可超过20MB",
						icon: 'none'
					})
					this.$refs.uploadFile.clearFiles(0)
				} else if (!['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg', 'pdf', 'doc',
						'docx'].includes(tempFiles[0].extname)) {
					uni.showToast({
						title: "请上传正确文件格式",
						icon: 'none'
					})
					this.$refs.uploadFile.clearFiles(0)
				} else {
					// #ifdef MP-WEIXIN
					uni.uploadFile({
						url: actionUrl, // 你的上传接口地址
						filePath: tempFilePaths[0],
						name: 'file', // 这里根据后端需要的字段来定义
						header: {
							Authorization: uni.getStorageSync('userToken')
						},
						formData: {
							'file': tempFiles[0].file // 其他要传的参数可以在这里添加
						},
						success: uploadFileRes => {
							let res = JSON.parse(uploadFileRes.data)
							console.log(res);
							this.formData.intentionPersonFile = res?.data.path || ''
							// 处理上传成功后的结果
						},
						fail: uploadFileError => {
							// 处理上传失败的情况
							this.formData.intentionPersonFile = ''
							console.error(uploadFileError);
						}
					});
					// #endif

					// #ifndef MP-WEIXIN
					let formData = new FormData()
					formData.append('file', tempFiles[0].file)
					uni.uploadFile({
						url: actionUrl, // 你的上传接口地址
						filePath: tempFilePaths[0],
						name: 'file', // 这里根据后端需要的字段来定义
						formData: {
							'file': formData // 其他要传的参数可以在这里添加
						},
						success: uploadFileRes => {
							let res = JSON.parse(uploadFileRes.data)
							console.log(res);
							this.formData.intentionPersonFile = res?.data.path || ''
							// 处理上传成功后的结果
						},
						fail: uploadFileError => {
							// 处理上传失败的情况
							this.formData.intentionPersonFile = ''
							console.error(uploadFileError);
						}
					});
					// #endif
				}
			}
		},
		onReady() {
			// 需要在onReady中设置规则
			this.$refs?.formVal?.setRules(this.rules)
		},
		async onShow() {

		},
		async onLoad(options) {
			this.companyId = options.companyId
			this.formData.firmManagementName = [null, 'null'].includes(options.cName) ? '-' : options.cName
			let userInfo = uni.getStorageSync('userInfo')
			this.userInfo = JSON.parse(userInfo)
			this.formData.intentionPersonName = this.userInfo?.nickName || ''
			this.formData.intentionPersonPhone = this.userInfo?.phone || ''
			console.log("options",options)
			if(options.gwId) {
				this.formData.firmManagementPostId = options.gwId
			}
			this.formData.firmStreet = options.firmStreet || ''
			this.initDict()
		}
	}
</script>

<style scoped lang="scss">
	.form-body {
		box-sizing: border-box;
		padding: 22upx;
		background-size: 100% 100%;
		background-repeat: no-repeat;
		position: relative;

		.form-content {
			height: 87vh;
			overflow: auto;
			box-sizing: border-box;
			padding: 22upx;
			background: #fff;
		}
	}

	.float-tips-num {
		float: right
	}

	.form_btn {
		background: #4989FD;
		border-radius: 16upx;
		font-weight: 600;
		font-size: 36upx;
		color: #FFFFFF;
		width: 85%;
		padding: 22upx;
		display: flex;
		justify-content: center;
		font-size: 32upx;
		position: fixed;
		bottom: 2%;
		left: 50%;
		transform: translateX(-50%);
	}
</style>