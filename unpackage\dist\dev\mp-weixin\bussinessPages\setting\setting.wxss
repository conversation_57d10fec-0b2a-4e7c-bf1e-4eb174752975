@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.mine-container.data-v-650e88b2 {
  display: flex;
  height: 100vh;
  overflow-y: auto;
  flex-direction: column;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background: #ECF3FF;
  /* min-height: 100vh; */
}
.menu-section.data-v-650e88b2 {
  flex: 1;
  padding: 30rpx;
  box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0, 0, 0, 0.04);
}
.menu-group.data-v-650e88b2 {
  background-color: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  overflow: hidden;
}
.menu-item.data-v-650e88b2 {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1px solid #f5f5f5;
}
.menu-item.data-v-650e88b2:last-child {
  border-bottom: none;
}
.menu-text.data-v-650e88b2 {
  flex: 1;
  margin-left: 20rpx;
  font-size: 32rpx;
  color: #333333;
}
