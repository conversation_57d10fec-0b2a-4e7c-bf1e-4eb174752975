<view class="search-container"><view class="search-header"><view class="dropdown-area"><view data-event-opts="{{[['tap',[['toggleStreetDropdown',['$event']]]]]}}" class="dropdown-trigger" bindtap="__e"><text class="dropdown-text">{{selectedStreet.text}}</text><uni-icons vue-id="35d93fe6-1" type="bottom" size="14" color="#000" bind:__l="__l"></uni-icons></view><block wx:if="{{showStreetDropdown}}"><view class="dropdown-menu"><block wx:for="{{streetList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectStreet',['$0'],[[['streetList','',index]]]]]]]}}" class="dropdown-item" bindtap="__e"><text>{{item.text}}</text></view></block></view></block></view><view class="search-input-area"><input class="search-input" type="text" placeholder="请输入关键字" data-event-opts="{{[['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindinput="__e"/></view><view data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-btn" bindtap="__e"><text>搜索</text></view></view></view>