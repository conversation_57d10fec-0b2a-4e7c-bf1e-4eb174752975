<template>
	<view class="content-box">
		<view class="tit">{{toggleTit}}</view>
		<image class="code-img" :src="toggleQr" @longpress="handleLongPress"></image>
		<view>长按识别二维码</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				toggleQr: '',
				toggleTit: ''
			}
		},
		methods: {
			// 处理长按事件
			handleLongPress() {
				// 调用微信小程序的API显示操作菜单
				uni.showActionSheet({
					itemList: ['转发给朋友', '保存到手机', '收藏'],
					success: (res) => {
						switch (res.tapIndex) {
							case 0: // 转发给朋友
								this.shareToFriend();
								break;
							case 1: // 保存到手机
								this.saveImageToPhone();
								break;
							case 2: // 收藏
								this.collectImage();
								break;
						}
					},
					fail: (err) => {
						console.log('操作取消', err);
					}
				});
			},

			// 转发给朋友
			shareToFriend() {
				// 在小程序中，可以使用button的open-type="share"来实现分享
				// 这里只是模拟该功能
				uni.showToast({
					title: '请点击右上角分享',
					icon: 'none'
				});
			},

			// 保存图片到手机
			saveImageToPhone() {
				// 获取图片信息
				uni.getImageInfo({
					src: this.toggleQr,
					success: (res) => {
						// 保存图片到相册
						uni.saveImageToPhotosAlbum({
							filePath: res.path,
							success: () => {
								uni.showToast({
									title: '保存成功',
									icon: 'success'
								});
							},
							fail: (err) => {
								// 如果失败可能是因为用户拒绝了授权
								if (err.errMsg.indexOf('auth deny') !== -1) {
									uni.showModal({
										title: '提示',
										content: '需要您授权保存相册',
										confirmText: '去设置',
										success: (res) => {
											if (res.confirm) {
												// 打开设置页面
												uni.openSetting();
											}
										}
									});
								} else {
									uni.showToast({
										title: '保存失败',
										icon: 'none'
									});
								}
							}
						});
					},
					fail: () => {
						uni.showToast({
							title: '获取图片信息失败',
							icon: 'none'
						});
					}
				});
			},

			// 收藏图片
			collectImage() {
				// 这里可以实现收藏功能，例如保存到本地存储或发送到服务器
				uni.showToast({
					title: '收藏成功',
					icon: 'success'
				});
			}
		},
		onLoad(options) {
			this.toggleTit = options.tit
			this.toggleQr = require(`../../static/images/${options.idx}.jpg`)
		}
	}
</script>

<style scoped>
	.content-box {
		display: flex;
		flex-direction: column;
		align-items: center;
		box-sizing: border-box;
		padding: 30upx
	}

	.tit {
		width: 100%;
		text-align: left
	}

	.code-img {
		width: 420upx;
		height: 420upx;
		object-fit: contain;
		border-radius: 16upx;
		box-shadow: 0 0 16upx #e8e8e8;
		margin-top: 100upx;
		/* background: orange; */
	}
</style>