<template>
	<view class="map-box">
		<!-- 查询bar -->
		<view>
			<searchBarVue @onSelect="handlerAreaSelect" @onSearch="handlerSearch" />
		</view>
		<liu-easy-map ref="liuEasyMap" :centerLat="centerTar.centerLat" :centerLng="centerTar.centerLng" :scale="14"
			:markerData="markerData" :polygons="polygons" :markerImgIn="markerImgIn" :goImgIn="goImgIn"
			:closeIcon="closeIcon" @clickMarker="markerClick" @regionchange="regionchange"></liu-easy-map>
	</view>
</template>

<script>
	import searchBarVue from '../../components/searchBar.vue';
	import {
		getServiceLists
	} from "@/api/rs.js"
	import {
		streetLists
	} from "./mock.js"
	export default {
		data() {
			return {
				markerData: [],
				//展示区域点位信息
				polygons: [{
					points: [{
						latitude: "24.931423",
						longitude: "118.649744"
					}], //经纬度数组
					strokeWidth: 2, //描边的宽度
					strokeColor: "#FF000060", //描边的颜色
					fillColor: "#FF000090" //填充颜色
				}, ],
				closeIcon: require("../../static/icons/close.png"),
				markerImgIn: require("../../static/icons/marker.png"),
				goImgIn: require("../../static/icons/go.png"),
				centerTar: {
					// 24.430047
					centerLat: '24.931423',
					// 117.020047
					centerLng: '118.649744'
				},
				body: {
					substreet: "",
					page: 1,
					pageSize: 10,
					total: 0,
					status: 0
				},
				streetLists,
				finished: false,
				isUseMyLocation: false
			};
		},
		components: {
			searchBarVue
		},
		methods: {
			handlerAreaSelect(val) {
				console.log('选11111111择区域', val.text)
				this.body.substreet = val.text == '全部街道' ? '' : val.text
				let centerPoint = this.streetLists[val.text]
				console.log('选择区域', val, centerPoint)
				this.streetPoint = {
					latitude: centerPoint.latitude,
					centerLng: centerPoint.longitude
				}
				this.centerTar.centerLat = centerPoint.latitude;
				this.centerTar.centerLng = centerPoint.longitude
				this.refresh()
			},
			handlerSearch(val) {
				console.log('搜索', val)
				this.body.serviceName = val.keyword
				this.refresh()
			},
			refresh() {
				this.markerData = []
				this.finished = false
				this.body.page = 1
				this.fetchMapLists()
			},
			regionchange() {
				if (!this.finished) {
					this.fetchMapLists()
				}
			},
			// 拉取地图点数据
			async fetchMapLists() {
				this.$refs?.liuEasyMap?.clearMarker()
				const {
					body
				} = this
				const res = await getServiceLists(body)
				console.log('pageConfig', res)
				let results = []
				this.body.total = res.total
				results = res.records
				this.markerData = this.markerData.concat(results)
				// 是否全部加载完
				if (this.markerData.length > this.body.total || this.markerData.length === this.body.total) {
					this.finished = true
				}
				this.markerData = this.markerData.map((item, idx) => {
					return {
						...item,
						id: idx,
						name: item.serviceName, //标记点展示名字
						address: item.serviceAddress,
						latitude: item.lat, //标记点纬度
						longitude: item.lon, //标记点经度
						// iconWidth: 32, //标记点图标宽度
						// iconHeight: 32, //标记点图标高度
						calloutColor: '#ffffff', //气泡窗口 文本颜色
						calloutFontSize: 14, //气泡窗口 文本大小
						calloutBorderRadius: 6, //气泡窗口 边框圆角
						calloutPadding: 8, //气泡窗口 文本边缘留白
						calloutBgColor: '#0B6CFF', //气泡窗口 背景颜色
						calloutDisplay: 'ALWAYS', //气泡窗口 展示类型 默认常显 'ALWAYS' 常显 'BYCLICK' 点击显示
					}
				})
				if (this.markerData) {
					let hasLatNode = this.markerData.filter(item => item.latitude)
					if (!this.isUseMyLocation) {
						this.centerTar = {
							centerLat: hasLatNode.length ? hasLatNode[0].latitude : this.streetPoint.latitude,
							// 117.020047
							centerLng: hasLatNode.length ? hasLatNode[0].longitude : this.streetPoint.centerLng
						}
					}
				};
				console.log('mockLats', this.markerData)
			},
			//点击标记点
			markerClick(e) {
				console.log('点击标记点信息1：', e)
			},
			// 获取定位
			getLocationCurrent() {
				let _ = this
				console.log("_.dataList", _.markerData)
				if (_.markerData.length) {
					let tar = _.markerData[0]
					if (tar.placeLatitude) {
						_.centerTar.centerLat = tar.placeLatitude;
						_.centerTar.centerLng = tar.placeLongitude;
					} else {
						console.log("7666666666")
						// #ifdef MP-WEIXIN
						this.getLocation()
						// #endif
						// #ifndef MP-WEIXIN
						this.getWebLocation()
						// #endif
					}
				} else {
					console.log("7666666666")
					// #ifdef MP-WEIXIN
					this.getLocation()
					// #endif
					// #ifndef MP-WEIXIN
					this.getWebLocation()
					// #endif
				}
			},
			// web端获取位置
			getWebLocation() {
				let _ = this
				// web端可直接获取到
				uni.getLocation({
					type: 'wgs84',
					success: function(res) {
						console.log('当前位置的经度：', res);
						_.centerTar.centerLat = res.latitude;
						_.centerTar.centerLng = res.longitude
						this.isUseMyLocation = true
					},
					fail(err) {
						console.log('错误：', err);
						this.isUseMyLocation = false
					}
				});
			},
			// 微信小程序授权
			getLocation() {
				let _ = this
				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('当前位置的经度：' + res.longitude);
						console.log('当前位置的纬度：' + res.latitude);
						_.centerTar.centerLng = res.longitude
						_.centerTar.centerLat = res.latitude
						this.isUseMyLocation = true
						// 调用后端接口根据得到的经纬度获取地址
						console.log(res, "根据经纬度获取地址");
					},
					// 若用户点击拒绝获取位置则弹出提示
					fail: (err) => {
						uni.showModal({
							content: '检测到您没打开获取位置功能权限，是否去设置打开？',
							confirmText: "确认",
							cancelText: '取消',
							success: (res) => {
								if (res.confirm) {
									uni.openSetting({
										success: (res) => {
											uni.showToast({
												title: '授权后请重新打开此页面',
												icon: 'none'
											})
										},
										fail: (err) => {
											console.log(err)
										}
									})
								} else {
									uni.showToast({
										title: '获取地理位置授权失败',
										icon: 'none',
										success: () => {
											this.isUseMyLocation = false
											// 返回上一页
											// setTimeout(() => {
											// 	uni.showToast({
											// 		title: "返回上一页",
											// 		icon: 'none'
											// 	})
											// }, 500)
										}
									})
								}
							}
						})
					},
				})
			},
		},
		onLoad() {
			this.refresh()
		},
		onShow() {
			this.getLocationCurrent()
		}
	}
</script>

<style lang="scss" scoped>
	.map-box {
		height: 100vh;
	}
</style>