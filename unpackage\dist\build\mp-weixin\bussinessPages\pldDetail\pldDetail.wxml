<view class="detail-container"><view class="company-header"><view class="company-basic-info"><view class="company-name">{{infos.firmName}}</view><view class="company-address"><uni-icons vue-id="4351e3ed-1" type="location" color="#fff" size="14" bind:__l="__l"></uni-icons><text>{{infos.firmAddress}}</text></view><view class="company-phone"><uni-icons vue-id="4351e3ed-2" type="phone" color="#fff" size="14" bind:__l="__l"></uni-icons><text>{{infos.contactTel}}</text></view></view></view><view class="company-intro"><view class="intro-title">公司介绍</view><view class="intro-list"><view class="intro-item"><rich-text nodes="{{infos.firmAbstract}}"></rich-text></view></view><view class="intro-title">工作环境</view><view class="intro-list"><view class="intro-item"><rich-text nodes="{{infos.firmEnv}}"></rich-text></view></view><z-paging class="vue-ref" vue-id="4351e3ed-3" auto="{{false}}" fixed="{{false}}" height="50vh" data-ref="paging" data-event-opts="{{[['^query',[['queryList']]],['^onRefresh',[['onRefresh']]]]}}" bind:query="__e" bind:onRefresh="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="cell" wx:for-index="idx" wx:key="idx"><leida-cell-vue vue-id="{{('4351e3ed-4-'+idx)+','+('4351e3ed-3')}}" obj="{{cell.a0}}" data-event-opts="{{[['^click',[['goDetail']]]]}}" bind:click="__e" bind:__l="__l"></leida-cell-vue></block></z-paging></view><block wx:if="{{$root.g0}}"><button data-event-opts="{{[['tap',[['goTJ',['$event']]]]]}}" class="fixed-btn" bindtap="__e">提交意向</button></block></view>