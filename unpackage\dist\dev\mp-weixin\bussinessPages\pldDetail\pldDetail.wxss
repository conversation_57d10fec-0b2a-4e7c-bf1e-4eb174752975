
.detail-container {
	background-color: #f5f7fa;
	min-height: 70vh;
	/* padding-bottom: 80px; */
	position: relative;
}
.fixed-btn {
	position: fixed;
	bottom: 5%;
	left: 5%;
	width: 90%;
	background-color: #4A89DC;
	color: #fff;
	border-radius: 50rpx;
}
.bac-icon {
	width: 34px;
	height: 34px;
}
.company-header {
	background-color: #4a89dc;
	color: #fff;
	padding: 20px 15px 30px;
	border-radius: 0 0 20px 20px;
	position: relative;
}
.back-icon {
	position: absolute;
	top: 10px;
	left: 0px;
}
.company-basic-info {
	padding-top: 20px;
}
.company-name {
	font-size: 18px;
	font-weight: bold;
	margin-bottom: 10px;
}
.company-address,
.company-phone {
	display: flex;
	align-items: center;
	font-size: 14px;
	margin-bottom: 5px;
}
.company-address .uni-icons,
.company-phone .uni-icons {
	margin-right: 5px;
}
.job-card {
	background-color: #fff;
	border-radius: 10px;
	margin: 15px;
	padding: 15px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.job-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 10px;
}
.job-desc {
	margin-bottom: 10px;
}
.job-item,
.intro-item {
	display: flex;
	margin-bottom: 5px;
	font-size: 14px;
	color: #666;
}
.item-num {
	margin-right: 5px;
	flex-shrink: 0;
}
.job-salary {
	text-align: right;
}
.salary-amount {
	font-size: 18px;
	font-weight: bold;
	color: #f56c6c;
}
.salary-unit {
	font-size: 14px;
	color: #999;
}
.company-intro {
	background-color: #fff;
	border-radius: 10px;
	margin: 15px;
	padding: 15px;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}
.intro-title {
	font-size: 16px;
	font-weight: bold;
	color: #333;
	margin-bottom: 10px;
}

