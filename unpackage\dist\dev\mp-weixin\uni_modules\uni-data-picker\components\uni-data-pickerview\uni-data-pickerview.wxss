@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.uni-data-pickerview {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
}
.error-text {
  color: #DD524D;
}
.loading-cover {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 1001;
}
.load-more {
  margin: auto;
}
.error-message {
  background-color: #fff;
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  padding: 15px;
  opacity: 0.9;
  z-index: 102;
}
.selected-list {
  display: flex;
  flex-wrap: nowrap;
  flex-direction: row;
  padding: 0 5px;
  border-bottom: 1px solid #f8f8f8;
}
.selected-item {
  margin-left: 10px;
  margin-right: 10px;
  padding: 12px 0;
  text-align: center;
  white-space: nowrap;
}
.selected-item-text-overflow {
  width: 168px;
  /* fix nvue */
  overflow: hidden;
  width: 6em;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.selected-item-active {
  border-bottom: 2px solid #007aff;
}
.selected-item-text {
  color: #007aff;
}
.tab-c {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}
.list {
  flex: 1;
}
.item {
  padding: 12px 15px;
  /* border-bottom: 1px solid #f0f0f0; */
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.is-disabled {
  opacity: 0.5;
}
.item-text {
  /* flex: 1; */
  color: #333333;
}
.item-text-overflow {
  width: 280px;
  /* fix nvue */
  overflow: hidden;
  width: 20em;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.check {
  margin-right: 5px;
  border: 2px solid #007aff;
  border-left: 0;
  border-top: 0;
  height: 12px;
  width: 6px;
  -webkit-transform-origin: center;
          transform-origin: center;
  transition: all 0.3s;
  -webkit-transform: rotate(45deg);
          transform: rotate(45deg);
}
