{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordDetail/tjRecordDetail.vue?034f", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordDetail/tjRecordDetail.vue?5e10", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordDetail/tjRecordDetail.vue?95ca", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordDetail/tjRecordDetail.vue?ba8d", "uni-app:///bussinessPages/tjRecordDetail/tjRecordDetail.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjRecordDetail/tjRecordDetail.vue?e37f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "name", "mixins", "data", "useLogin", "infos", "gwLists", "loading", "finished", "refreshing", "useQueryPage", "useFunCallApi", "body", "page", "pageSize", "total", "status", "firmManagementId", "com", "methods", "isImageFile", "filePath", "file", "goDetailPolicy", "uni", "urls", "goBack", "goTJ", "url", "content", "success", "goDetail", "jumpDetailCompany", "onShow", "onLoad", "companyInfo"], "mappings": "uKAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,mDACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCjCvB,yHAAuwB,eAAG,G,qJCiD1wB,QAKA,WACA,W,EACA,CACAC,iBACAjB,cACAkB,6BACAC,gBACA,OACAC,YACAC,SACAC,WACAC,WACAC,YACAC,cACAC,gBACAC,0BAAA,iCACAC,MACAC,OACAC,YACAC,QACAC,SACAC,qBAEAC,SAGAC,SACAC,wBACA,MACA,IACAC,EAEAC,EAFAD,SAGA,yCACA,mBAGAE,2BACA,oBACAC,gBACAC,oBAEA,gCAEAC,kBACAF,kBAEAG,gBACA,cACAH,cACAI,0HAGAJ,aACAK,0BACAC,oBACAzC,qBACA,UACAmC,aACAI,6BAOAG,qBACA1C,yBACAmC,cACAI,wOAGAI,6BACAR,cACAI,sEAIAK,kBACA,iCACA,gCACA,mBAEAC,mBAAA,WACA,OACA,IACA,2KAGA,OAFA7C,oBAEA,oBACA,iDAAA8C,SACA9C,wBACA,mDACA,mDAPA,MAUA,c,6DCtJA,yHAAklC,eAAG,G", "file": "bussinessPages/tjRecordDetail/tjRecordDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './bussinessPages/tjRecordDetail/tjRecordDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tjRecordDetail.vue?vue&type=template&id=e3e3c4f6&\"\nvar renderjs\nimport script from \"./tjRecordDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./tjRecordDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tjRecordDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/tjRecordDetail/tjRecordDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjRecordDetail.vue?vue&type=template&id=e3e3c4f6&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjRecordDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjRecordDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"detail-container\">\r\n\t\t<!-- 顶部公司信息区域 -->\r\n\t\t<view class=\"company-header\">\r\n\t\t\t<!-- <image @click=\"goBack\" src=\"../rsjDir/bac-icon.png\" class=\"bac-icon\" /> -->\r\n\t\t\t<view class=\"company-basic-info\">\r\n\t\t\t\t<view class=\"company-name\">{{ infos.firmManagementName }}</view>\r\n\t\t\t\t<view class=\"company-address\">\r\n\t\t\t\t\t<uni-icons type=\"location\" color=\"#fff\" size=\"14\" />\r\n\t\t\t\t\t<text>{{ com.firmAddress }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"company-phone\">\r\n\t\t\t\t\t<uni-icons type=\"phone\" color=\"#fff\" size=\"14\" />\r\n\t\t\t\t\t<text>{{ com.contactTel }}</text>\r\n\t\t\t\t\t<view class=\"float-detail\" @click=\"jumpDetailCompany\">详情 <uni-icons type=\"right\" color=\"#fff\"\r\n\t\t\t\t\t\t\tsize=\"14\"></uni-icons></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 公司介绍 -->\r\n\t\t<view class=\"company-intro\">\r\n\t\t\t<view class=\"job-info\">\r\n\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">求职意向：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{infos.firmManagementPostName || '-'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">您的姓名：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{infos.intentionPersonName || '-'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">您的手机号：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{infos.intentionPersonPhone || '-'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">提交时间：</text>\r\n\t\t\t\t\t<text class=\"info-value\">{{infos.createTime || '-'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t<text class=\"info-label\">您的简历：</text>\r\n\t\t\t\t\t<text class=\"info-value\" v-if=\"infos.intentionPersonFile\" style=\"color:#4A89DC\"\r\n\t\t\t\t\t\t@click=\"goDetailPolicy({filePath:infos.intentionPersonFile})\">简历文件</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetIntentionDetail,\r\n\t\tgetCompanyDetail,\r\n\t\tgetPositionLists\r\n\t} from '@/api/rs.js'\r\n\timport table from '../../mixins/table';\r\n\timport file from '../../mixins/file';\r\n\texport default {\r\n\t\tname: 'pLdDetail',\r\n\t\tcomponents: {},\r\n\t\tmixins: [table, file],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tuseLogin: false,\r\n\t\t\t\tinfos: {},\r\n\t\t\t\tgwLists: [],\r\n\t\t\t\tloading: false,\r\n\t\t\t\tfinished: true,\r\n\t\t\t\trefreshing: false,\r\n\t\t\t\tuseQueryPage: false,\r\n\t\t\t\tuseFunCallApi: (data) => getPositionLists(data),\r\n\t\t\t\tbody: {\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tpageSize: 10,\r\n\t\t\t\t\ttotal: 0,\r\n\t\t\t\t\tstatus: 0,\r\n\t\t\t\t\tfirmManagementId: ''\r\n\t\t\t\t},\r\n\t\t\t\tcom: {}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tisImageFile(file) {\r\n\t\t\t\tif (file) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tfilePath\r\n\t\t\t\t\t} =\r\n\t\t\t\t\tfile\r\n\t\t\t\t\tconst pattern = /\\.(jpg|jpeg|png|gif|bmp|tiff|webp)$/i;\r\n\t\t\t\t\treturn pattern.test(filePath);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoDetailPolicy(item) {\r\n\t\t\t\tif (this.isImageFile(item)){\r\n\t\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t\turls: [item.filePath]\r\n\t\t\t\t\t})\r\n\t\t\t\t}else this.handlerOpenPriviewFile(item)\r\n\t\t\t},\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack()\r\n\t\t\t},\r\n\t\t\tgoTJ() {\r\n\t\t\t\tif (this.useLogin) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/bussinessPages/tjForm/tjForm?cName=${this.infos.firmName}&companyId=${this.body.firmManagementId}`\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\tcontent: \"暂未登录，请先登录后再进行操作\",\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\tconsole.log(\"r3s\", res)\r\n\t\t\t\t\t\t\tif (res.cancel) {} else {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/mine/mine'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tconsole.log(\"111111 \", item)\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/bussinessPages/gwDetail/gwDetail?id=${item.id}&companyId=${this.body.firmManagementId}&cName=${this.infos.firmName}&cAddress=${this.infos.firmAddress}&cTel=${this.infos.firmLegalTel}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tjumpDetailCompany() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/bussinessPages/pldDetail/pldDetail?id=${this.com.id}`\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\t\t\tconst userToken = uni.getStorageSync('userToken')\r\n\t\t\tthis.useLogin = userToken ? true : false\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tlet id = options.id\r\n\t\t\tif (id) {\r\n\t\t\t\tgetIntentionDetail(id).then(async res => {\r\n\t\t\t\t\tconsole.log(\"详情\", res)\r\n\t\t\t\t\t// 企业详情\r\n\t\t\t\t\tthis.infos = res\r\n\t\t\t\t\tconst companyInfo = await getCompanyDetail(res.firmManagementId)\r\n\t\t\t\t\tconsole.log(\"gons详情\", companyInfo)\r\n\t\t\t\t\tthis.com = companyInfo\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.detail-container {\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tmin-height: 70vh;\r\n\t\t/* padding-bottom: 80px; */\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.fixed-btn {\r\n\t\tposition: fixed;\r\n\t\tbottom: 5%;\r\n\t\tleft: 5%;\r\n\t\twidth: 90%;\r\n\t\tbackground-color: #4A89DC;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\r\n\t.bac-icon {\r\n\t\twidth: 34px;\r\n\t\theight: 34px;\r\n\t}\r\n\r\n\t.company-header {\r\n\t\tbackground-color: #4a89dc;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 20px 15px 30px;\r\n\t\tborder-radius: 0 0 20px 20px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.back-icon {\r\n\t\tposition: absolute;\r\n\t\ttop: 10px;\r\n\t\tleft: 0px;\r\n\t}\r\n\r\n\t.float-detail {\r\n\t\tfont-size: 28upx;\r\n\t\tposition: absolute;\r\n\t\tright: 3%;\r\n\t}\r\n\r\n\t.company-basic-info {\r\n\t\tpadding-top: 20px;\r\n\t}\r\n\r\n\t.company-name {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.company-address,\r\n\t.company-phone {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 14px;\r\n\t\tmargin-bottom: 5px;\r\n\t}\r\n\r\n\t.company-address .uni-icons,\r\n\t.company-phone .uni-icons {\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.job-card {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10px;\r\n\t\tmargin: 15px;\r\n\t\tpadding: 15px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.job-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.job-desc {\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.job-item,\r\n\t.intro-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 5px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.item-num {\r\n\t\tmargin-right: 5px;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.job-info-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 10px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.job-salary {\r\n\t\ttext-align: right;\r\n\t}\r\n\r\n\t.info-label {\r\n\t\tflex-shrink: 0;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.salary-amount {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #f56c6c;\r\n\t}\r\n\r\n\t.salary-unit {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.company-intro {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10px;\r\n\t\tmargin: 15px;\r\n\t\tpadding: 15px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.intro-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjRecordDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjRecordDetail.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}