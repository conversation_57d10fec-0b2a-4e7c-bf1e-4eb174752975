(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/updateMine/updateMine"],{"31da":function(e,t,n){"use strict";n.r(t);var r=n("bee0"),a=n("adcd");for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);n("44b5");var o=n("828b"),u=Object(o["a"])(a["default"],r["b"],r["c"],!1,null,"3fb0adac",null,!1,r["a"],void 0);t["default"]=u.exports},"44b5":function(e,t,n){"use strict";var r=n("f120"),a=n.n(r);a.a},"6bc0":function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("7eb4")),i=r(n("7ca3")),o=r(n("ee10")),u=(n("8f59"),n("3ec1"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,i.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var l=n("e7b9"),d={data:function(){return{formData:{nickName:"",age:"",code:"",birthdate:"",sex:1},photo:[],items:[{text:"一年级",value:"1-0",children:[{text:"1.1班",value:"1-1"},{text:"1.2班",value:"1-2"}]},{text:"二年级",value:"2-0"},{text:"三年级",value:"3-0"}],addressLists:[],educationalList:[],jobList:[],idCardTypeOptions:[],rules:{nickName:{rules:[{required:!0,errorMessage:"请输入姓名"},{maxLength:100,errorMessage:"姓名长度最长 {maxLength} 个字符"}]},sex:{rules:[{required:!0,errorMessage:"请选择性别"}]},birthdate:{rules:[{required:!0,errorMessage:"请选择出生年月日"}]},desiredLocationName:{rules:[{required:!0,errorMessage:"请选择求职意向地"}]},educationLevel:{rules:[{required:!0,errorMessage:"请选择学历水平"}]},desiredField:{rules:[{required:!0,errorMessage:"请选择求职意向"}]}}}},computed:{},watch:{formData:{handler:function(e){},deep:!0,immediate:!0}},methods:{initDict:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n,r;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,u.getDictionary)("jobInterest");case 2:return n=t.sent,t.next=5,(0,u.getDictionary)("educationalType");case 5:r=t.sent,console.log("jobList",n),e.jobList=null===n||void 0===n?void 0:n.map((function(e){return{value:e.entryCode,text:e.entryName}})),console.log("educationalList",r),e.educationalList=null===r||void 0===r?void 0:r.map((function(e){return{value:e.entryCode,text:e.entryName}}));case 10:case"end":return t.stop()}}),t)})))()},initApiLists:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,(0,u.getOrgData)();case 2:n=t.sent,e.addressLists=n?e.formatTreeData(n):[],console.log("获取居住地数据",e.addressLists);case 4:case"end":return t.stop()}}),t)})))()},onchangeAddress:function(e){var t=e.detail.value;console.log("onchangeAddress",t),this.formData.address=t},onchangeDesiredLocation:function(e){var t=e.detail.value;console.log("onchangeDesiredLocation",t),this.formData.desiredLocationName=t},formatTreeData:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"name",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"id",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"children";return e&&Array.isArray(e)?e.map((function(e){var i=c(c({},e),{},{text:e[n]||"",value:e[r]||""});return e[a]&&Array.isArray(e[a])&&(i[a]=t.formatTreeData(e[a],n,r,a)),i})):[]},onnodeclick:function(e){},submitForm:function(){var t=this.formData;console.log("formData",t),this.$refs.formVal.validate().then(function(){var n=(0,o.default)(a.default.mark((function n(r){var i;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.address=JSON.stringify(t.address),t.desiredLocationName=JSON.stringify(t.desiredLocationName),t.birthdate="".concat(t.birthdate," 00:00:00"),console.log("表单数据信息：",r),n.next=6,(0,u.editUser)(t);case 6:i=n.sent,console.log("修改用户信息",i),i&&(e.setStorageSync("userInfo",JSON.stringify(t)),e.showToast({title:"修改成功",icon:"success"}),setTimeout((function(){e.navigateBack()}),500));case 9:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}()).catch((function(e){console.log("表单错误信息：",e)}))},fileSelectDiy:function(t){var n=this;return(0,o.default)(a.default.mark((function r(){var i,o,u,s;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:i=t.tempFiles,o=t.tempFilePaths,u=l.baseApi,s="".concat(u,"/api/zero/wx/user/uploadAvatar"),console.log("diy",i,o,l,s),i[0].size>2097152?(e.showToast({title:"图片大小不可超过2MB",icon:"none"}),n.$refs.uploadFile.clearFiles(0)):e.uploadFile({url:s,filePath:o[0],name:"file",header:{Authorization:e.getStorageSync("userToken")},formData:{file:i[0].file},success:function(e){var t=JSON.parse(e.data);console.log(t),n.formData.avatarUrl=(null===t||void 0===t?void 0:t.data.path)||""},fail:function(e){console.error(e)}});case 5:case"end":return r.stop()}}),r)})))()}},onReady:function(){var e,t;null===(e=this.$refs)||void 0===e||null===(t=e.formVal)||void 0===t||t.setRules(this.rules)},onShow:function(){return(0,o.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()},onLoad:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.initDict(),e.initApiLists(),t.next=4,(0,u.getUserInfo)();case 4:n=t.sent,e.formData=c(c({},n),{},{sex:n.sex?n.sex:"",address:n.address?JSON.parse(n.address):[],desiredLocationName:n.desiredLocationName?JSON.parse(n.desiredLocationName):[]}),console.log("getUserInfo",e.formData),e.photo=e.formData.avatarUrl?[{name:"头像",extname:"jpg",url:e.formData.avatarUrl}]:[];case 8:case"end":return t.stop()}}),t)})))()}};t.default=d}).call(this,n("df3c")["default"])},a9fb:function(e,t,n){"use strict";(function(e,t){var r=n("47a9");n("bfec");r(n("3240"));var a=r(n("31da"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},adcd:function(e,t,n){"use strict";n.r(t);var r=n("6bc0"),a=n.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);t["default"]=a.a},bee0:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return r}));var r={uniForms:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(n.bind(null,"36f4"))},uniFormsItem:function(){return n.e("uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(n.bind(null,"6078"))},uniFilePicker:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker")]).then(n.bind(null,"2611"))},uniEasyinput:function(){return n.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(n.bind(null,"d129"))},uniDataCheckbox:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox")]).then(n.bind(null,"8c73"))},uniDatetimePicker:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker")]).then(n.bind(null,"e731"))},uniDataPicker:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker")]).then(n.bind(null,"0a57"))},uniDataSelect:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-data-select/components/uni-data-select/uni-data-select")]).then(n.bind(null,"c625"))}},a=function(){var e=this,t=e.$createElement,n=(e._self._c,e.formData.nickName?e.formData.nickName.length:null),r=e.formData.detailedAddress?e.formData.detailedAddress.length:null,a=e.formData.graduationSchool?e.formData.graduationSchool.length:null,i=e.formData.skillsLevel?e.formData.skillsLevel.length:null,o=e.formData.workExperience?e.formData.workExperience.length:null;e.$mp.data=Object.assign({},{$root:{g0:n,g1:r,g2:a,g3:i,g4:o}})},i=[]},f120:function(e,t,n){}},[["a9fb","common/runtime","common/vendor"]]]);