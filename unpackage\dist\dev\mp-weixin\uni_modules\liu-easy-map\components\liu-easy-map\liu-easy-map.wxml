<view style="width:100%;height:100%;"><map style="width:100%;height:100%;" id="esaymap" scale="{{scale}}" latitude="{{centerLat}}" longitude="{{centerLng}}" markers="{{markers}}" polygons="{{polygonsData}}" enable-zoom="{{isEnableZoom}}" enable-scroll="{{isEnableScroll}}" enable-satellite="{{isShowWxMap}}" enable-rotate="{{isEnableRotate}}" data-event-opts="{{[['markertap',[['chooseItem',['$event']]]],['tap',[['clickMap',['$event']]]],['regionchange',[['$emit',['regionchange']]]]]}}" bindmarkertap="__e" bindtap="__e" bindregionchange="__e"></map><view hidden="{{!(false)}}" class="rightbox"><view data-event-opts="{{[['tap',[['changeTab',[1]]]]]}}" class="boxitem" bindtap="__e"><image class="itemimg" src="{{tabIndex?myaddressOnImg:myaddressImg}}" mode></image><view class="{{['itemname',tabIndex?'active':'']}}">我的位置</view></view><block wx:if="{{wxMapShow}}"><view data-event-opts="{{[['tap',[['changeTab',[2]]]]]}}" class="boxitem" bindtap="__e"><image class="itemimg" src="{{tabIndex2?wxmapOnImg:wxmapImg}}" mode></image><view class="{{['itemname',tabIndex2?'active':'']}}">卫星地图</view></view></block></view><block wx:if="{{isShowDetail}}"><cover-view class="detailbox"><cover-view data-event-opts="{{[['tap',[['closeDetail',['$event']]]]]}}" class="closeicon" bindtap="__e">关闭</cover-view><cover-view class="boxl"><cover-view class="boxlhd ellipsis">{{detailData.name||'--'}}</cover-view><cover-view class="boxlbd ellipsis">{{detailData.address||'--'}}</cover-view></cover-view><cover-view data-event-opts="{{[['tap',[['goRoute',['$event']]]]]}}" class="boxr" bindtap="__e">去导航</cover-view></cover-view></block></view>