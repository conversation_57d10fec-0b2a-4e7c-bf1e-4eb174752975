<template>
	<view class="cell-box" @click="$emit('onDetail', obj)">
		<view class="mark-num">{{ obj.idx + 1 }}</view>
		<view class="cell-content">
			<view class="title">{{ obj.fileName }}</view>
			<view class="desc esllipsis">{{ obj.issuerStr }}</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "PolicyCell",
		props: {
			obj: {
				type: Object,
				default: () => {
					return {
						idx: 0,
						fileName: '',
						issuerStr: ''
					}
				}
			}
		},
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.cell-box {
		height: 172upx;
		width: 686upx;
		display: flex;
		align-items: flex-start;
		background: linear-gradient(180deg, #ffffff 0%, #eef9ff 100%);
		box-shadow: 0px 2px 8px 0px rgba(174, 198, 230, 0.5);
		border-radius: 32upx;
		margin: 16upx auto auto;
		justify-content: center;
		box-sizing: border-box;
		padding: 28upx 0 0;

		.mark-num {
			width: 88upx;
			height: 116upx;
			background: url('data:image/png;base64,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') no-repeat;
			background-size: 100% 100%;
			font-weight: bold;
			font-size: 52upx;
			color: #ffffff;
			text-align: center;
			line-height: 64upx;
		}

		.cell-content {
			width: 500upx;
			margin-left: 38upx;
			// background: yellow;

			.title {
				font-weight: 600;
				font-size: 32upx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				-webkit-line-clamp: 2;
			}

			.desc {
				font-weight: 400;
				font-size: 28upx;;
				color: #999999;
				margin-top: 16upx;
				overflow: hidden;
				/* 隐藏超出容器的内容 */
				text-overflow: ellipsis;
				/* 用省略号表示溢出的文本 */
				white-space: nowrap;
				/* 确保文本不换行 */
			}
		}
	}
</style>