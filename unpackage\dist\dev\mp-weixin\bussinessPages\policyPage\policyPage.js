(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/policyPage/policyPage"],{84:function(e,n,t){"use strict";(function(e,n){var r=t(4);t(26);r(t(25));var o=r(t(85));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t(1)["default"],t(2)["createPage"])},85:function(e,n,t){"use strict";t.r(n);var r=t(86),o=t(88);for(var i in o)["default"].indexOf(i)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(i);t(91);var c,a=t(32),u=Object(a["default"])(o["default"],r["render"],r["staticRenderFns"],!1,null,"c0415136",null,!1,r["components"],c);u.options.__file="bussinessPages/policyPage/policyPage.vue",n["default"]=u.exports},86:function(e,n,t){"use strict";t.r(n);var r=t(87);t.d(n,"render",(function(){return r["render"]})),t.d(n,"staticRenderFns",(function(){return r["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return r["recyclableRender"]})),t.d(n,"components",(function(){return r["components"]}))},87:function(e,n,t){"use strict";var r;t.r(n),t.d(n,"render",(function(){return o})),t.d(n,"staticRenderFns",(function(){return c})),t.d(n,"recyclableRender",(function(){return i})),t.d(n,"components",(function(){return r}));try{r={zPaging:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/z-paging/components/z-paging/z-paging")]).then(t.bind(null,224))}}}catch(a){if(-1===a.message.indexOf("Cannot find module")||-1===a.message.indexOf(".vue"))throw a;console.error(a.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var e=this,n=e.$createElement,t=(e._self._c,e.__map(e.dataList,(function(n,t){var r=e.__get_orig(n),o=Object.assign({},n,{idx:t});return{$orig:r,a0:o}})));e.$mp.data=Object.assign({},{$root:{l0:t}})},i=!1,c=[];o._withStripped=!0},88:function(e,n,t){"use strict";t.r(n);var r=t(89),o=t.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(i);n["default"]=o.a},89:function(e,n,t){"use strict";var r=t(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=r(t(90)),i=r(t(48)),c=function(){t.e("bussinessPages/policyPage/policyCell").then(function(){return resolve(t(255))}.bind(null,t)).catch(t.oe)},a={data:function(){return{body:{areaCodeList:["350503"],isShow:1,state:1,page:1,pageSize:10,total:0}}},mixins:[o.default,i.default],components:{policyCellVue:c},onLoad:function(){this.fetchList()}};n.default=a},91:function(e,n,t){"use strict";t.r(n);var r=t(92),o=t.n(r);for(var i in r)["default"].indexOf(i)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(i);n["default"]=o.a},92:function(e,n,t){}},[[84,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/bussinessPages/policyPage/policyPage.js.map