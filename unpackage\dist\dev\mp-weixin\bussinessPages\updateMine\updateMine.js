(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/updateMine/updateMine"],{100:function(e,n,t){"use strict";t.r(n);var r=t(101),a=t.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(o);n["default"]=a.a},101:function(e,n,t){},93:function(e,n,t){"use strict";(function(e,n){var r=t(4);t(26);r(t(25));var a=r(t(94));e.__webpack_require_UNI_MP_PLUGIN__=t,n(a.default)}).call(this,t(1)["default"],t(2)["createPage"])},94:function(e,n,t){"use strict";t.r(n);var r=t(95),a=t(97);for(var o in a)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(o);t(100);var i,s=t(32),u=Object(s["default"])(a["default"],r["render"],r["staticRenderFns"],!1,null,"2a04bef6",null,!1,r["components"],i);u.options.__file="bussinessPages/updateMine/updateMine.vue",n["default"]=u.exports},95:function(e,n,t){"use strict";t.r(n);var r=t(96);t.d(n,"render",(function(){return r["render"]})),t.d(n,"staticRenderFns",(function(){return r["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return r["recyclableRender"]})),t.d(n,"components",(function(){return r["components"]}))},96:function(e,n,t){"use strict";var r;t.r(n),t.d(n,"render",(function(){return a})),t.d(n,"staticRenderFns",(function(){return i})),t.d(n,"recyclableRender",(function(){return o})),t.d(n,"components",(function(){return r}));try{r={uniForms:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-forms/components/uni-forms/uni-forms")]).then(t.bind(null,262))},uniFormsItem:function(){return t.e("uni_modules/uni-forms/components/uni-forms-item/uni-forms-item").then(t.bind(null,275))},uniFilePicker:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker")]).then(t.bind(null,282))},uniEasyinput:function(){return t.e("uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput").then(t.bind(null,296))},uniDataCheckbox:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-data-checkbox/components/uni-data-checkbox/uni-data-checkbox")]).then(t.bind(null,303))},uniDatetimePicker:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-datetime-picker/components/uni-datetime-picker/uni-datetime-picker")]).then(t.bind(null,310))},uniDataPicker:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-data-picker/components/uni-data-picker/uni-data-picker")]).then(t.bind(null,322))},uniDataSelect:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-data-select/components/uni-data-select/uni-data-select")]).then(t.bind(null,330))}}}catch(s){if(-1===s.message.indexOf("Cannot find module")||-1===s.message.indexOf(".vue"))throw s;console.error(s.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var a=function(){var e=this,n=e.$createElement,t=(e._self._c,e.formData.nickName?e.formData.nickName.length:null),r=e.formData.detailedAddress?e.formData.detailedAddress.length:null,a=e.formData.graduationSchool?e.formData.graduationSchool.length:null,o=e.formData.skillsLevel?e.formData.skillsLevel.length:null,i=e.formData.workExperience?e.formData.workExperience.length:null;e.$mp.data=Object.assign({},{$root:{g0:t,g1:r,g2:a,g3:o,g4:i}})},o=!1,i=[];a._withStripped=!0},97:function(e,n,t){"use strict";t.r(n);var r=t(98),a=t.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(o);n["default"]=a.a},98:function(e,n,t){"use strict";(function(e){var r=t(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=r(t(37)),o=r(t(11)),i=r(t(39)),s=(t(99),t(47));function u(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter((function(n){return Object.getOwnPropertyDescriptor(e,n).enumerable}))),t.push.apply(t,r)}return t}function c(e){for(var n=1;n<arguments.length;n++){var t=null!=arguments[n]?arguments[n]:{};n%2?u(Object(t),!0).forEach((function(n){(0,o.default)(e,n,t[n])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):u(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}))}return e}var l=t(34),d={data:function(){return{formData:{nickName:"",age:"",code:"",birthdate:"",sex:1},photo:[],items:[{text:"一年级",value:"1-0",children:[{text:"1.1班",value:"1-1"},{text:"1.2班",value:"1-2"}]},{text:"二年级",value:"2-0"},{text:"三年级",value:"3-0"}],addressLists:[],educationalList:[],jobList:[],idCardTypeOptions:[],rules:{nickName:{rules:[{required:!0,errorMessage:"请输入姓名"},{maxLength:100,errorMessage:"姓名长度最长 {maxLength} 个字符"}]},sex:{rules:[{required:!0,errorMessage:"请选择性别"}]},birthdate:{rules:[{required:!0,errorMessage:"请选择出生年月日"}]},desiredLocationName:{rules:[{required:!0,errorMessage:"请选择求职意向地"}]},educationLevel:{rules:[{required:!0,errorMessage:"请选择学历水平"}]},desiredField:{rules:[{required:!0,errorMessage:"请选择求职意向"}]}}}},computed:{},watch:{formData:{handler:function(e){},deep:!0,immediate:!0}},methods:{initDict:function(){var e=this;return(0,i.default)(a.default.mark((function n(){var t,r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,s.getDictionary)("jobInterest");case 2:return t=n.sent,n.next=5,(0,s.getDictionary)("educationalType");case 5:r=n.sent,console.log("jobList",t),e.jobList=null===t||void 0===t?void 0:t.map((function(e){return{value:e.entryCode,text:e.entryName}})),console.log("educationalList",r),e.educationalList=null===r||void 0===r?void 0:r.map((function(e){return{value:e.entryCode,text:e.entryName}}));case 10:case"end":return n.stop()}}),n)})))()},initApiLists:function(){var e=this;return(0,i.default)(a.default.mark((function n(){var t;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,(0,s.getOrgData)();case 2:t=n.sent,e.addressLists=t?e.formatTreeData(t):[],console.log("获取居住地数据",e.addressLists);case 4:case"end":return n.stop()}}),n)})))()},onchangeAddress:function(e){var n=e.detail.value;console.log("onchangeAddress",n),this.formData.address=n},onchangeDesiredLocation:function(e){var n=e.detail.value;console.log("onchangeDesiredLocation",n),this.formData.desiredLocationName=n},formatTreeData:function(e){var n=this,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"name",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"id",a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"children";return e&&Array.isArray(e)?e.map((function(e){var o=c(c({},e),{},{text:e[t]||"",value:e[r]||""});return e[a]&&Array.isArray(e[a])&&(o[a]=n.formatTreeData(e[a],t,r,a)),o})):[]},onnodeclick:function(e){},submitForm:function(){var n=this.formData;console.log("formData",n),this.$refs.formVal.validate().then(function(){var t=(0,i.default)(a.default.mark((function t(r){var o;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return n.address=JSON.stringify(n.address),n.desiredLocationName=JSON.stringify(n.desiredLocationName),n.birthdate="".concat(n.birthdate," 00:00:00"),console.log("表单数据信息：",r),t.next=6,(0,s.editUser)(n);case 6:o=t.sent,console.log("修改用户信息",o),o&&(e.setStorageSync("userInfo",JSON.stringify(n)),e.showToast({title:"修改成功",icon:"success"}),setTimeout((function(){e.navigateBack()}),500));case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).catch((function(e){console.log("表单错误信息：",e)}))},fileSelectDiy:function(n){var t=this;return(0,i.default)(a.default.mark((function r(){var o,i,s,u;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:o=n.tempFiles,i=n.tempFilePaths,s=l.baseApi,u="".concat(s,"/api/zero/wx/user/uploadAvatar"),console.log("diy",o,i,l,u),o[0].size>2097152?(e.showToast({title:"图片大小不可超过2MB",icon:"none"}),t.$refs.uploadFile.clearFiles(0)):e.uploadFile({url:u,filePath:i[0],name:"file",header:{Authorization:e.getStorageSync("userToken")},formData:{file:o[0].file},success:function(e){var n=JSON.parse(e.data);console.log(n),t.formData.avatarUrl=(null===n||void 0===n?void 0:n.data.path)||""},fail:function(e){console.error(e)}});case 5:case"end":return r.stop()}}),r)})))()}},onReady:function(){var e,n;null===(e=this.$refs)||void 0===e||null===(n=e.formVal)||void 0===n||n.setRules(this.rules)},onShow:function(){return(0,i.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)})))()},onLoad:function(){var e=this;return(0,i.default)(a.default.mark((function n(){var t;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return e.initDict(),e.initApiLists(),n.next=4,(0,s.getUserInfo)();case 4:t=n.sent,e.formData=c(c({},t),{},{sex:t.sex?t.sex:"",address:t.address?JSON.parse(t.address):[],desiredLocationName:t.desiredLocationName?JSON.parse(t.desiredLocationName):[]}),console.log("getUserInfo",e.formData),e.photo=e.formData.avatarUrl?[{name:"头像",extname:"jpg",url:e.formData.avatarUrl}]:[];case 8:case"end":return n.stop()}}),n)})))()}};n.default=d}).call(this,t(2)["default"])}},[[93,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/bussinessPages/updateMine/updateMine.js.map