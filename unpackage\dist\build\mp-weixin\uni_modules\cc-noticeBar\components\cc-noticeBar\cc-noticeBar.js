(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar"],{2982:function(n,t,e){},c14d:function(n,t,e){"use strict";var c=e("2982"),i=e.n(c);i.a},d51d:function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return o})),e.d(t,"a",(function(){return c}));var c={uniIcons:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(e.bind(null,"2642"))}},i=function(){var n=this.$createElement;this._self._c},o=[]},d5b0:function(n,t,e){"use strict";e.r(t);var c=e("e0f9"),i=e.n(c);for(var o in c)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return c[n]}))}(o);t["default"]=i.a},e0f9:function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var c={data:function(){return{}},components:{},props:{colors:{type:String,default:"#333"},noticeList:{type:Array}},methods:{itemClick:function(n){this.$emit("click",n)}}};t.default=c},f61b:function(n,t,e){"use strict";e.r(t);var c=e("d51d"),i=e("d5b0");for(var o in i)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(o);e("c14d");var u=e("828b"),r=Object(u["a"])(i["default"],c["b"],c["c"],!1,null,"71ff0e46",null,!1,c["a"],void 0);t["default"]=r.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar-create-component',
    {
        'uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("f61b"))
        })
    },
    [['uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar-create-component']]
]);
