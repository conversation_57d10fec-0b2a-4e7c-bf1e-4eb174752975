{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjForm/tjForm.vue?2b2a", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjForm/tjForm.vue?f208", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjForm/tjForm.vue?0971", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjForm/tjForm.vue?0748", "uni-app:///bussinessPages/tjForm/tjForm.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/tjForm/tjForm.vue?3d32"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniForms", "uniFormsItem", "uniEasyinput", "uniDataSelect", "uniFilePicker", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "formData", "intentionPersonName", "length", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "firmManagementName", "firmManagementPostId", "firmManagementPostName", "intentionPersonPhone", "intentionPersonFile", "firmStreet", "userInfo", "companyId", "photo", "items", "text", "value", "children", "jobList", "rules", "required", "errorMessage", "max<PERSON><PERSON><PERSON>", "sex", "birthdate", "educationLevel", "desired<PERSON>ield", "computed", "watch", "handler", "deep", "immediate", "methods", "initDict", "firmManagementId", "status", "postChange", "formatTreeData", "node", "newNode", "textField", "valueField", "childrenField", "onnodeclick", "submitForm", "wxUserId", "putRes", "uni", "title", "icon", "setTimeout", "fileSelectDiy", "tempFiles", "tempFilePaths", "baseApi", "config", "actionUrl", "url", "filePath", "name", "header", "Authorization", "success", "fail", "onReady", "onShow", "onLoad"], "mappings": "uJAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,mCACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,wHAITC,aAAc,WACZ,OAAO,8FAITC,aAAc,WACZ,OAAO,gGAITC,cAAe,WACb,OAAO,0IAITC,cAAe,WACb,OAAO,2IAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,SAASC,oBAClBR,EAAIO,SAASC,oBAAoBC,OACjC,MACJT,EAAIU,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLV,GAAIA,MAKRW,GAAmB,EACnBC,EAAkB,GACtBjB,EAAOkB,eAAgB,G,iCChEvB,yHAA+vB,eAAG,G,gKC6ClwB,GAHA,MAGA,OAGA,2kBAPA,cAQA,CACAN,gBAeA,OACAJ,UACAW,sBACAC,wBACAC,0BACAZ,uBACAa,wBACAC,uBACAC,eAEAC,cACAC,aACAC,SACAC,QACAC,WACAC,YACAC,WACAF,YACAC,aAEA,CACAD,YACAC,eAIA,CACAD,WACAC,aAEA,CACAD,WACAC,cAGAE,WACAC,OACAxB,qBACAwB,QACAC,YACAC,sBAEA,CACAC,cACAD,yCAIAE,KACAJ,QACAC,YACAC,wBAGAG,WACAL,QACAC,YACAC,2BAGAf,sBACAa,QACAC,YACAC,0BAGAI,gBACAN,QACAC,YACAC,0BAGAK,cACAP,QACAC,YACAC,6BAMAM,YAGAC,OACAlC,UACAmC,sBACAC,QACAC,eAGAC,SACAC,oBAAA,2JACA,uBACAC,6BACAC,WACA,OAHAjB,SAIAlC,yBACA,yDACA,OACAgC,WACAD,oBAGA,yHAZA,IAcAqB,uBACA,4DACApD,4BACA,4DAEAqD,2BAAA,6MACA,2BAIA,mBAEA,aACAC,OACAvB,cACAC,iBAaA,OATA,4BACAuB,sBACAD,KACAE,EACAC,EACAC,IAIA,KArBA,IAwBAC,0BACAC,sBAAA,WAEAlD,EACA,KADAA,SAEAV,0BACA,8KACA,OAAAA,yBAAA,UACA,yBACAU,OACAwC,6BACAW,8BACA,OAJAC,SAKA9D,wBACA,IACA+D,aACAC,aACAC,iBAEAC,uBACAH,mBACA,MACA,2CACA,mDAjBA,IAiBA,mBACA/D,6BAIAmE,0BAGA,gJAFAC,cACAC,kBAGAC,EACAC,EADAD,QAEAE,6CACAxE,2BACA,oBACA+D,aACAC,qBACAC,cAEA,kCACA,gEACA,+BAQAF,cACAU,MACAC,cACAC,YACAC,QACAC,6CAEAnE,UACA,gBAEAoE,oBACA,yBACA9E,eACA,8EAGA+E,iBAEA,kCACA/E,qBA1BA+D,aACAC,kBACAC,cAEA,kCAkDA,0CApEA,KAuEAe,mBAAA,QAEA,6FAEAC,kBAAA,gKAGAC,mBAAA,8IACA,wBACA,0EACAvD,+BACA,yBACA,yFACA,uFACA3B,yBACA,SACA,wCAEA,uCACA,wDAZA,KAcA,c,6DCrUA,yHAAk7C,eAAG,G", "file": "bussinessPages/tjForm/tjForm.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './bussinessPages/tjForm/tjForm.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./tjForm.vue?vue&type=template&id=e5d6fb76&scoped=true&\"\nvar renderjs\nimport script from \"./tjForm.vue?vue&type=script&lang=js&\"\nexport * from \"./tjForm.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tjForm.vue?vue&type=style&index=0&id=e5d6fb76&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e5d6fb76\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/tjForm/tjForm.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjForm.vue?vue&type=template&id=e5d6fb76&scoped=true&\"", "var components\ntry {\n  components = {\n    uniForms: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms/uni-forms\" */ \"@/uni_modules/uni-forms/components/uni-forms/uni-forms.vue\"\n      )\n    },\n    uniFormsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-forms/components/uni-forms-item/uni-forms-item\" */ \"@/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.vue\"\n      )\n    },\n    uniEasyinput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput\" */ \"@/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\n      )\n    },\n    uniDataSelect: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-select/components/uni-data-select/uni-data-select\" */ \"@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue\"\n      )\n    },\n    uniFilePicker: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker\" */ \"@/uni_modules/uni-file-picker/components/uni-file-picker/uni-file-picker.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.formData.intentionPersonName\n    ? _vm.formData.intentionPersonName.length\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjForm.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjForm.vue?vue&type=script&lang=js&\"", "<!--提交意向-->\r\n<template>\r\n\t<view class=\"form-body\" style=\"padding: 0;\">\r\n\t\t<view class=\"form-content\">\r\n\t\t\t<uni-forms validateTrigger='bind' :modelValue=\"formData\" ref=\"formVal\" label-position=\"top\"\r\n\t\t\t\tlabel-width=\"200\">\r\n\t\t\t\t<uni-forms-item label=\"公司名称\" name=\"firmManagementName\">\r\n\t\t\t\t\t<uni-easyinput maxlength=\"100\" type=\"textarea\" v-model=\"formData.firmManagementName\"\r\n\t\t\t\t\t\tplaceholder=\"请输入公司名称\" />\r\n\t\t\t\t\t<!-- \t\t\t\t\t<view class=\"float-tips-num\"><text\r\n\t\t\t\t\t\t\tstyle=\"color: #CACACA;\">{{formData.firmManagementName ? formData.firmManagementName.length: 0}}</text>/100\r\n\t\t\t\t\t</view> -->\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"求职意向\" name=\"firmManagementPostId\" required>\r\n\t\t\t\t\t<uni-data-select v-model=\"formData.firmManagementPostId\" :localdata=\"jobList\"\r\n\t\t\t\t\t\t@change=\"postChange\"></uni-data-select>\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"您的姓名\" name=\"intentionPersonName\" required>\r\n\t\t\t\t\t<uni-easyinput type=\"text\" maxLength=\"100\" v-model=\"formData.intentionPersonName\"\r\n\t\t\t\t\t\tplaceholder=\"请输入姓名\" />\r\n\t\t\t\t\t<view class=\"float-tips-num\"><text\r\n\t\t\t\t\t\t\tstyle=\"color: #CACACA;\">{{formData.intentionPersonName ? formData.intentionPersonName.length: 0}}</text>/100\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"您的手机号\" name=\"intentionPersonPhone\">\r\n\t\t\t\t\t<uni-easyinput :disabled=\"false\" type=\"number\" maxlength=\"11\"\r\n\t\t\t\t\t\tv-model=\"formData.intentionPersonPhone\" placeholder=\"请输入联系人手机\" />\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t\t<uni-forms-item label=\"您的简历\">\r\n\t\t\t\t\t<uni-file-picker ref=\"uploadFile\" v-model=\"photo\" file-mediatype=\"all\" :auto-upload=\"false\"\r\n\t\t\t\t\t\tmode=\"grid\" :limit=\"1\" @progress=\"fileProgress\" @success=\"fileSuccess\" @fail=\"fileFail\"\r\n\t\t\t\t\t\t@select=\"fileSelectDiy\" />\r\n\t\t\t\t\t<view style=\"color:#999999\">注意: 图片、PDF、word附件，限制20M以内，限制一份</view>\r\n\t\t\t\t</uni-forms-item>\r\n\t\t\t</uni-forms>\r\n\t\t</view>\r\n\t\t<view class=\"btn btn_primary form_btn\" @click=\"submitForm\">提交</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tlet config = require('../../utils/' + process.env.NODE_ENV)\r\n\timport {\r\n\t\tmapGetters\r\n\t} from 'vuex'\r\n\timport {\r\n\t\taddIntention,\r\n\t\tgetPostListNames\r\n\t} from \"@/api/rs\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\t// 测试身份证号\r\n\t\t\tfunction validateIdCard(rule, idCard, data, cb) {\r\n\t\t\t\tconst idCardRegex = /^(?:\\d{15}|\\d{17}[\\dXx])$/;\r\n\t\t\t\tif (!idCardRegex.test(idCard)) {\r\n\t\t\t\t\tcb(\"请输入正确的证件号\");\r\n\t\t\t\t} else return true;\r\n\t\t\t}\r\n\t\t\t// 测试邮箱\r\n\t\t\tfunction validateEmail(rule, email, data, cb) {\r\n\t\t\t\tconst emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$/;\r\n\t\t\t\tif (email.length && !emailRegex.test(email)) {\r\n\t\t\t\t\tcb(\"请输入正确的邮箱\");\r\n\t\t\t\t} else return true;\r\n\t\t\t}\r\n\t\t\treturn {\r\n\t\t\t\tformData: {\r\n\t\t\t\t\tfirmManagementName: '',\r\n\t\t\t\t\tfirmManagementPostId: '',\r\n\t\t\t\t\tfirmManagementPostName: '',\r\n\t\t\t\t\tintentionPersonName: '',\r\n\t\t\t\t\tintentionPersonPhone: '',\r\n\t\t\t\t\tintentionPersonFile: '',\r\n\t\t\t\t\tfirmStreet: ''\r\n\t\t\t\t},\r\n\t\t\t\tuserInfo: null,\r\n\t\t\t\tcompanyId: '',\r\n\t\t\t\tphoto: [],\r\n\t\t\t\titems: [{\r\n\t\t\t\t\t\ttext: \"一年级\",\r\n\t\t\t\t\t\tvalue: \"1-0\",\r\n\t\t\t\t\t\tchildren: [{\r\n\t\t\t\t\t\t\t\ttext: \"1.1班\",\r\n\t\t\t\t\t\t\t\tvalue: \"1-1\"\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\ttext: \"1.2班\",\r\n\t\t\t\t\t\t\t\tvalue: \"1-2\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: \"二年级\",\r\n\t\t\t\t\t\tvalue: \"2-0\"\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: \"三年级\",\r\n\t\t\t\t\t\tvalue: \"3-0\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tjobList: [], //求职意向\r\n\t\t\t\trules: {\r\n\t\t\t\t\tintentionPersonName: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\t\terrorMessage: '请输入姓名',\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\tmaxLength: 100,\r\n\t\t\t\t\t\t\t\terrorMessage: '姓名长度最长 {maxLength} 个字符',\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t},\r\n\t\t\t\t\tsex: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\terrorMessage: '请选择性别',\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbirthdate: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\terrorMessage: '请选择出生年月日',\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfirmManagementPostId: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\terrorMessage: '请选择求职意向',\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t},\r\n\t\t\t\t\teducationLevel: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\terrorMessage: '请选择学历水平',\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t},\r\n\t\t\t\t\tdesiredField: {\r\n\t\t\t\t\t\trules: [{\r\n\t\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\t\terrorMessage: '请选择求职意向',\r\n\t\t\t\t\t\t}]\r\n\t\t\t\t\t},\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomputed: {\r\n\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tformData: {\r\n\t\t\t\thandler(val) {},\r\n\t\t\t\tdeep: true,\r\n\t\t\t\timmediate: true,\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync initDict() {\r\n\t\t\t\tconst jobList = await getPostListNames({\r\n\t\t\t\t\tfirmManagementId: this.companyId,\r\n\t\t\t\t\tstatus: 1\r\n\t\t\t\t})\r\n\t\t\t\tconsole.log(\"jobList\", jobList)\r\n\t\t\t\tthis.jobList = jobList?.map(el => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tvalue: el.id,\r\n\t\t\t\t\t\ttext: el.postName\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t\tif(this.formData.firmManagementPostId) { this.postChange(this.formData.firmManagementPostId) }\r\n\t\t\t},\r\n\t\t\tpostChange(e) {\r\n\t\t\t\tlet tar = this.jobList.filter(el => el.value == e)\r\n\t\t\t\tconsole.log(\"postChange\", tar)\r\n\t\t\t\tthis.formData.firmManagementPostName = tar.length ? tar[0].text : ''\r\n\t\t\t},\r\n\t\t\tformatTreeData(treeData, textField = 'name', valueField = 'id', childrenField = 'children') {\r\n\t\t\t\tif (!treeData || !Array.isArray(treeData)) {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn treeData.map(node => {\r\n\t\t\t\t\t// 创建新节点，避免修改原始数据\r\n\t\t\t\t\tconst newNode = {\r\n\t\t\t\t\t\t...node,\r\n\t\t\t\t\t\ttext: node[textField] || '',\r\n\t\t\t\t\t\tvalue: node[valueField] || ''\r\n\t\t\t\t\t};\r\n\r\n\t\t\t\t\t// 如果有子节点，递归处理\r\n\t\t\t\t\tif (node[childrenField] && Array.isArray(node[childrenField])) {\r\n\t\t\t\t\t\tnewNode[childrenField] = this.formatTreeData(\r\n\t\t\t\t\t\t\tnode[childrenField],\r\n\t\t\t\t\t\t\ttextField,\r\n\t\t\t\t\t\t\tvalueField,\r\n\t\t\t\t\t\t\tchildrenField\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\treturn newNode;\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonnodeclick(node) {},\r\n\t\t\tsubmitForm() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tformData\r\n\t\t\t\t} = this\r\n\t\t\t\tconsole.log(\"formData\", formData)\r\n\t\t\t\tthis.$refs.formVal.validate().then(async res => {\r\n\t\t\t\t\tconsole.log('表单数据信息：', res);\r\n\t\t\t\t\tconst putRes = await addIntention({\r\n\t\t\t\t\t\t...formData,\r\n\t\t\t\t\t\tfirmManagementId: this.companyId,\r\n\t\t\t\t\t\twxUserId: this.userInfo.userId\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconsole.log(\"修改用户信息\", putRes)\r\n\t\t\t\t\tif (putRes) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: \"提交成功\",\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\t\t}, 500);\r\n\t\t\t\t\t}\r\n\t\t\t\t}).catch(err => {\r\n\t\t\t\t\tconsole.log('表单错误信息：', err);\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 上传文件\r\n\t\t\tasync fileSelectDiy({\r\n\t\t\t\ttempFiles,\r\n\t\t\t\ttempFilePaths\r\n\t\t\t}) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tbaseApi\r\n\t\t\t\t} = config\r\n\t\t\t\tconst actionUrl = `${baseApi}/api/v1/common/uploadAvatar`;\r\n\t\t\t\tconsole.log(\"diy\", tempFiles, tempFilePaths, config, actionUrl)\r\n\t\t\t\tif (tempFiles[0].size > 20 * 1024 * 1024) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"文件大小不可超过20MB\",\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$refs.uploadFile.clearFiles(0)\r\n\t\t\t\t} else if (!['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'tiff', 'svg', 'pdf', 'doc',\r\n\t\t\t\t\t\t'docx'].includes(tempFiles[0].extname)) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: \"请上传正确文件格式\",\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.$refs.uploadFile.clearFiles(0)\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: actionUrl, // 你的上传接口地址\r\n\t\t\t\t\t\tfilePath: tempFilePaths[0],\r\n\t\t\t\t\t\tname: 'file', // 这里根据后端需要的字段来定义\r\n\t\t\t\t\t\theader: {\r\n\t\t\t\t\t\t\tAuthorization: uni.getStorageSync('userToken')\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\t'file': tempFiles[0].file // 其他要传的参数可以在这里添加\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsuccess: uploadFileRes => {\r\n\t\t\t\t\t\t\tlet res = JSON.parse(uploadFileRes.data)\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\tthis.formData.intentionPersonFile = res?.data.path || ''\r\n\t\t\t\t\t\t\t// 处理上传成功后的结果\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: uploadFileError => {\r\n\t\t\t\t\t\t\t// 处理上传失败的情况\r\n\t\t\t\t\t\t\tthis.formData.intentionPersonFile = ''\r\n\t\t\t\t\t\t\tconsole.error(uploadFileError);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\r\n\t\t\t\t\t// #ifndef MP-WEIXIN\r\n\t\t\t\t\tlet formData = new FormData()\r\n\t\t\t\t\tformData.append('file', tempFiles[0].file)\r\n\t\t\t\t\tuni.uploadFile({\r\n\t\t\t\t\t\turl: actionUrl, // 你的上传接口地址\r\n\t\t\t\t\t\tfilePath: tempFilePaths[0],\r\n\t\t\t\t\t\tname: 'file', // 这里根据后端需要的字段来定义\r\n\t\t\t\t\t\tformData: {\r\n\t\t\t\t\t\t\t'file': formData // 其他要传的参数可以在这里添加\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tsuccess: uploadFileRes => {\r\n\t\t\t\t\t\t\tlet res = JSON.parse(uploadFileRes.data)\r\n\t\t\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t\t\tthis.formData.intentionPersonFile = res?.data.path || ''\r\n\t\t\t\t\t\t\t// 处理上传成功后的结果\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: uploadFileError => {\r\n\t\t\t\t\t\t\t// 处理上传失败的情况\r\n\t\t\t\t\t\t\tthis.formData.intentionPersonFile = ''\r\n\t\t\t\t\t\t\tconsole.error(uploadFileError);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t// 需要在onReady中设置规则\r\n\t\t\tthis.$refs?.formVal?.setRules(this.rules)\r\n\t\t},\r\n\t\tasync onShow() {\r\n\r\n\t\t},\r\n\t\tasync onLoad(options) {\r\n\t\t\tthis.companyId = options.companyId\r\n\t\t\tthis.formData.firmManagementName = [null, 'null'].includes(options.cName) ? '-' : options.cName\r\n\t\t\tlet userInfo = uni.getStorageSync('userInfo')\r\n\t\t\tthis.userInfo = JSON.parse(userInfo)\r\n\t\t\tthis.formData.intentionPersonName = this.userInfo?.nickName || ''\r\n\t\t\tthis.formData.intentionPersonPhone = this.userInfo?.phone || ''\r\n\t\t\tconsole.log(\"options\",options)\r\n\t\t\tif(options.gwId) {\r\n\t\t\t\tthis.formData.firmManagementPostId = options.gwId\r\n\t\t\t}\r\n\t\t\tthis.formData.firmStreet = options.firmStreet || ''\r\n\t\t\tthis.initDict()\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t.form-body {\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 22upx;\r\n\t\tbackground-size: 100% 100%;\r\n\t\tbackground-repeat: no-repeat;\r\n\t\tposition: relative;\r\n\r\n\t\t.form-content {\r\n\t\t\theight: 87vh;\r\n\t\t\toverflow: auto;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\tpadding: 22upx;\r\n\t\t\tbackground: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.float-tips-num {\r\n\t\tfloat: right\r\n\t}\r\n\r\n\t.form_btn {\r\n\t\tbackground: #4989FD;\r\n\t\tborder-radius: 16upx;\r\n\t\tfont-weight: 600;\r\n\t\tfont-size: 36upx;\r\n\t\tcolor: #FFFFFF;\r\n\t\twidth: 85%;\r\n\t\tpadding: 22upx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 32upx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 2%;\r\n\t\tleft: 50%;\r\n\t\ttransform: translateX(-50%);\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjForm.vue?vue&type=style&index=0&id=e5d6fb76&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./tjForm.vue?vue&type=style&index=0&id=e5d6fb76&scoped=true&lang=scss&\""], "sourceRoot": ""}