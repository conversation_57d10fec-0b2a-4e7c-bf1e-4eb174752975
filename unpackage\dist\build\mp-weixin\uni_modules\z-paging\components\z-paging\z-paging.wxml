<wxs src="./wxs/z-paging-wxs.wxs" module="pagingWxs"></wxs>
<view class="{{['data-v-36d10d5c',(true)?'z-paging-content':'',(!usePageScroll)?'z-paging-content-full':'',(!usePageScroll&&fixed)?'z-paging-content-fixed':'',(usePageScroll)?'z-paging-content-page':'',(renderPropScrollTop<1)?'z-paging-reached-top':'',(useChatRecordMode)?'z-paging-use-chat-record-mode':'']}}" style="{{$root.s0}}"><block wx:if="{{cssSafeAreaInsetBottom===-1}}"><view class="zp-safe-area-inset-bottom data-v-36d10d5c"></view></block><block wx:if="{{showF2&&showRefresherF2}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="zp-f2-content data-v-36d10d5c" style="{{'transform:'+(f2Transform)+';'+('transition:'+('transform .2s linear')+';')+('height:'+(superContentHeight+'px')+';')+('z-index:'+(f2ZIndex)+';')}}" catchtouchmove="__e"><slot name="f2"></slot></view></block><block wx:if="{{!usePageScroll&&zSlots.top}}"><slot name="top"></slot></block><block wx:else><block wx:if="{{usePageScroll&&zSlots.top}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="zp-page-top data-v-36d10d5c" style="{{'top:'+(windowTop+'px')+';'+('z-index:'+(topZIndex)+';')}}" catchtouchmove="__e"><slot name="top"></slot></view></block></block><view class="{{['data-v-36d10d5c',(true)?'zp-view-super':'',(!usePageScroll)?'zp-scroll-view-super':'']}}" style="{{$root.s1}}"><block wx:if="{{zSlots.left}}"><view class="{{['data-v-36d10d5c',(true)?'zp-page-left':'',(finalIsOldWebView)?'zp-absoulte':'']}}"><slot name="left"></slot></view></block><view class="{{['data-v-36d10d5c',(true)?'zp-scroll-view-container':'',(finalIsOldWebView)?'zp-absoulte':'']}}" style="{{$root.s2}}"><scroll-view class="{{['data-v-36d10d5c','vue-ref',(true)?'zp-scroll-view':'',(!usePageScroll)?'zp-scroll-view-absolute':'',(!showScrollbar)?'zp-scroll-view-hide-scrollbar':'']}}" style="{{$root.s3}}" scroll-top="{{scrollTop}}" scroll-left="{{scrollLeft}}" scroll-x="{{scrollX}}" scroll-y="{{finalScrollable}}" enable-back-to-top="{{finalEnableBackToTop}}" show-scrollbar="{{showScrollbar}}" scroll-with-animation="{{finalScrollWithAnimation}}" scroll-into-view="{{scrollIntoView}}" lower-threshold="{{finalLowerThreshold}}" upper-threshold="{{5}}" refresher-enabled="{{finalRefresherEnabled&&!useCustomRefresher}}" refresher-threshold="{{finalRefresherThreshold}}" refresher-default-style="{{finalRefresherDefaultStyle}}" refresher-background="{{refresherBackground}}" refresher-triggered="{{finalRefresherTriggered}}" data-ref="zp-scroll-view" data-event-opts="{{[['scroll',[['_scroll',['$event']]]],['scrolltolower',[['_onScrollToLower',['$event']]]],['scrolltoupper',[['_onScrollToUpper',['$event']]]],['refresherrestore',[['_onRestore',['$event']]]],['refresherrefresh',[['_onRefresh',[true]]]]]}}" bindscroll="__e" bindscrolltolower="__e" bindscrolltoupper="__e" bindrefresherrestore="__e" bindrefresherrefresh="__e"><view class="zp-paging-touch-view data-v-36d10d5c" bindtouchstart="{{pagingWxs.touchstart}}" bindtouchmove="{{pagingWxs.touchmove}}" bindtouchend="{{pagingWxs.touchend}}" bindtouchcancel="{{pagingWxs.touchend}}" bindmousedown="{{pagingWxs.mousedown}}" bindmousemove="{{pagingWxs.mousemove}}" bindmouseup="{{pagingWxs.mouseup}}" bindmouseleave="{{pagingWxs.mouseleave}}"><block wx:if="{{finalRefresherFixedBacHeight>0}}"><view class="zp-fixed-bac-view data-v-36d10d5c" style="{{'background:'+(refresherFixedBackground)+';'+('height:'+(finalRefresherFixedBacHeight+'px')+';')}}"></view></block><view class="zp-paging-main data-v-36d10d5c" style="{{$root.s4}}" change:prop="{{pagingWxs.propObserver}}" prop="{{wxsPropType}}" data-refresherThreshold="{{finalRefresherThreshold}}" data-refresherF2Enabled="{{refresherF2Enabled}}" data-refresherF2Threshold="{{finalRefresherF2Threshold}}" data-isIos="{{isIos}}" data-loading="{{loading||isRefresherInComplete}}" data-useChatRecordMode="{{useChatRecordMode}}" data-refresherEnabled="{{refresherEnabled}}" data-useCustomRefresher="{{useCustomRefresher}}" data-pageScrollTop="{{wxsPageScrollTop}}" data-scrollTop="{{wxsScrollTop}}" data-refresherMaxAngle="{{refresherMaxAngle}}" data-refresherNoTransform="{{refresherNoTransform}}" data-refresherAecc="{{refresherAngleEnableChangeContinued}}" data-usePageScroll="{{usePageScroll}}" data-watchTouchDirectionChange="{{watchTouchDirectionChange}}" data-oldIsTouchmoving="{{isTouchmoving}}" data-refresherOutRate="{{finalRefresherOutRate}}" data-refresherPullRate="{{finalRefresherPullRate}}" data-hasTouchmove="{{hasTouchmove}}"><block wx:if="{{showRefresher}}"><view class="zp-custom-refresher-view data-v-36d10d5c" style="{{'margin-top:'+('-'+(finalRefresherThreshold+refresherThresholdUpdateTag)+'px')+';'+('background:'+(refresherBackground)+';')+('opacity:'+(isTouchmoving?1:0)+';')}}"><view class="zp-custom-refresher-container data-v-36d10d5c" style="{{'height:'+(finalRefresherThreshold+'px')+';'+('background:'+(refresherBackground)+';')}}"><block wx:if="{{useRefresherStatusBarPlaceholder}}"><view class="zp-custom-refresher-status-bar-placeholder data-v-36d10d5c" style="{{'height:'+(statusBarHeight+'px')+';'}}"></view></block><view class="zp-custom-refresher-slot-view data-v-36d10d5c"><block wx:if="{{!(zSlots.refresherComplete&&refresherStatus===R.Complete)&&!(zSlots.refresherF2&&refresherStatus===R.GoF2)}}"><slot name="refresher"></slot><scoped-slots-refresher refresherStatus="{{refresherStatus}}" class="scoped-ref" bind:__l="__l"></scoped-slots-refresher></block></view><block wx:if="{{zSlots.refresherComplete&&refresherStatus===R.Complete}}"><slot name="refresherComplete"></slot></block><block wx:else><block wx:if="{{zSlots.refresherF2&&refresherStatus===R.GoF2}}"><slot name="refresherF2"></slot></block><block wx:else><block wx:if="{{!showCustomRefresher}}"><z-paging-refresh class="zp-custom-refresher-refresh data-v-36d10d5c vue-ref" style="{{'height:'+(finalRefresherThreshold-finalRefresherThresholdPlaceholder+'px')+';'}}" vue-id="0f3cceb5-1" status="{{refresherStatus}}" defaultThemeStyle="{{finalRefresherThemeStyle}}" defaultText="{{finalRefresherDefaultText}}" isIos="{{isIos}}" pullingText="{{finalRefresherPullingText}}" refreshingText="{{finalRefresherRefreshingText}}" completeText="{{finalRefresherCompleteText}}" goF2Text="{{finalRefresherGoF2Text}}" defaultImg="{{refresherDefaultImg}}" pullingImg="{{refresherPullingImg}}" refreshingImg="{{refresherRefreshingImg}}" completeImg="{{refresherCompleteImg}}" refreshingAnimated="{{refresherRefreshingAnimated}}" showUpdateTime="{{showRefresherUpdateTime}}" updateTimeKey="{{refresherUpdateTimeKey}}" updateTimeTextMap="{{finalRefresherUpdateTimeTextMap}}" imgStyle="{{refresherImgStyle}}" titleStyle="{{refresherTitleStyle}}" updateTimeStyle="{{refresherUpdateTimeStyle}}" unit="{{unit}}" data-ref="refresh" bind:__l="__l"></z-paging-refresh></block></block></block></view></view></block><view class="zp-paging-container data-v-36d10d5c" style="{{'justify-content:'+(useChatRecordMode?'flex-end':'flex-start')+';'}}"><block wx:if="{{showLoading&&zSlots.loading&&!loadingFullFixed}}"><slot name="loading"></slot></block><view class="zp-paging-container-content data-v-36d10d5c" style="{{$root.s5}}"><slot></slot><block wx:if="{{finalUseInnerList}}"><slot name="header"></slot><view class="zp-list-container data-v-36d10d5c" style="{{$root.s6}}"><block wx:if="{{finalUseVirtualList}}"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="zp_unique_index"><view class="zp-list-cell data-v-36d10d5c" style="{{item.s7}}" id="{{fianlVirtualCellIdPrefix+'-'+item.$orig[virtualCellIndexKey]}}" data-event-opts="{{[['tap',[['_innerCellClick',['$0',virtualTopRangeIndex+index],[[['virtualList','zp_unique_index',item.$orig['zp_unique_index']]]]]]]]}}" bindtap="__e"><block wx:if="{{useCompatibilityMode}}"><view class="data-v-36d10d5c">使用兼容模式请在组件源码z-paging.vue第103行中注释这一行，并打开下面一行注释</view></block><block wx:else><slot name="cell"></slot><scoped-slots-cell item="{{item.$orig}}" index="{{virtualTopRangeIndex+index}}" class="scoped-ref" bind:__l="__l"></scoped-slots-cell></block></view></block></block><block wx:else><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['_innerCellClick',['$0',index],[[['realTotalData','',index]]]]]]]}}" class="zp-list-cell data-v-36d10d5c" bindtap="__e"><slot name="cell"></slot><scoped-slots-cell item="{{item.$orig}}" index="{{index}}" class="scoped-ref" bind:__l="__l"></scoped-slots-cell></view></block></block></view><slot name="footer"></slot></block><block wx:if="{{$root.g0}}"><view style="{{$root.s8}}" class="data-v-36d10d5c"><block wx:if="{{loadingStatus===M.NoMore&&zSlots.chatNoMore}}"><slot name="chatNoMore"></slot></block><block wx:else><block wx:if="{{zSlots.chatLoading}}"><slot name="chatLoading"></slot><scoped-slots-chatLoading loadingMoreStatus="{{loadingStatus}}" class="scoped-ref" bind:__l="__l"></scoped-slots-chatLoading></block><block wx:else><z-paging-load-more vue-id="0f3cceb5-2" zConfig="{{zLoadMoreConfig}}" data-event-opts="{{[['^doClick',[['_onLoadingMore',['click']]]]]}}" bind:doClick="__e" class="data-v-36d10d5c" bind:__l="__l"></z-paging-load-more></block></block></view></block><block wx:if="{{useVirtualList}}"><view class="zp-virtual-placeholder data-v-36d10d5c" style="{{'height:'+(virtualPlaceholderBottomHeight+'px')+';'}}"></view></block><block wx:if="{{showLoadingMoreDefault}}"><slot name="loadingMoreDefault"></slot></block><block wx:else><block wx:if="{{showLoadingMoreLoading}}"><slot name="loadingMoreLoading"></slot></block><block wx:else><block wx:if="{{showLoadingMoreNoMore}}"><slot name="loadingMoreNoMore"></slot></block><block wx:else><block wx:if="{{showLoadingMoreFail}}"><slot name="loadingMoreFail"></slot></block><block wx:else><block wx:if="{{showLoadingMoreCustom}}"><z-paging-load-more vue-id="0f3cceb5-3" zConfig="{{zLoadMoreConfig}}" data-event-opts="{{[['^doClick',[['_onLoadingMore',['click']]]]]}}" bind:doClick="__e" class="data-v-36d10d5c" bind:__l="__l"></z-paging-load-more></block></block></block></block></block><block wx:if="{{safeAreaInsetBottom&&useSafeAreaPlaceholder&&!useChatRecordMode}}"><view class="zp-safe-area-placeholder data-v-36d10d5c" style="{{'height:'+(safeAreaBottom+'px')+';'}}"></view></block></view><block wx:if="{{showEmpty}}"><view class="{{['data-v-36d10d5c',(true)?'zp-empty-view':'',(emptyViewCenter)?'zp-empty-view-center':'']}}" style="{{$root.s9}}"><block wx:if="{{zSlots.empty}}"><slot name="empty"></slot><scoped-slots-empty isLoadFailed="{{isLoadFailed}}" class="scoped-ref" bind:__l="__l"></scoped-slots-empty></block><block wx:else><z-paging-empty-view vue-id="0f3cceb5-4" emptyViewImg="{{finalEmptyViewImg}}" emptyViewText="{{finalEmptyViewText}}" showEmptyViewReload="{{finalShowEmptyViewReload}}" emptyViewReloadText="{{finalEmptyViewReloadText}}" isLoadFailed="{{isLoadFailed}}" emptyViewStyle="{{emptyViewStyle}}" emptyViewTitleStyle="{{emptyViewTitleStyle}}" emptyViewImgStyle="{{emptyViewImgStyle}}" emptyViewReloadStyle="{{emptyViewReloadStyle}}" emptyViewZIndex="{{emptyViewZIndex}}" emptyViewFixed="{{emptyViewFixed}}" unit="{{unit}}" data-event-opts="{{[['^reload',[['_emptyViewReload']]],['^viewClick',[['_emptyViewClick']]]]}}" bind:reload="__e" bind:viewClick="__e" class="data-v-36d10d5c" bind:__l="__l"></z-paging-empty-view></block></view></block></view></view></view></scroll-view></view><block wx:if="{{zSlots.right}}"><view class="{{['data-v-36d10d5c',(true)?'zp-page-right':'',(finalIsOldWebView)?'zp-absoulte zp-right':'']}}"><slot name="right"></slot></view></block></view><view class="zp-page-bottom-container data-v-36d10d5c" style="{{'background:'+(bottomBgColor)+';'}}"><block wx:if="{{!usePageScroll&&zSlots.bottom}}"><slot name="bottom"></slot></block><block wx:else><block wx:if="{{usePageScroll&&zSlots.bottom}}"><view data-event-opts="{{[['touchmove',[['',['$event']]]]]}}" class="zp-page-bottom data-v-36d10d5c" style="{{'bottom:'+(windowBottom+'px')+';'}}" catchtouchmove="__e"><slot name="bottom"></slot></view></block></block><block wx:if="{{useChatRecordMode&&autoAdjustPositionWhenChat}}"><view style="{{'height:'+(chatRecordModeSafeAreaBottom+'px')+';'}}" class="data-v-36d10d5c"></view><view class="zp-page-bottom-keyboard-placeholder-animate data-v-36d10d5c" style="{{'height:'+(keyboardHeight+'px')+';'}}"></view></block></view><block wx:if="{{showBackToTopClass}}"><view data-event-opts="{{[['tap',[['_backToTopClick',['$event']]]]]}}" class="{{['data-v-36d10d5c',finalBackToTopClass]}}" style="{{$root.s10}}" catchtap="__e"><block wx:if="{{zSlots.backToTop}}"><slot name="backToTop"></slot></block><block wx:else><image class="{{['zp-back-to-top-img','data-v-36d10d5c',($root.g1)?'zp-back-to-top-img-inversion':'']}}" src="{{$root.g2?backToTopImg:base64BackToTop}}"></image></block></view></block><block wx:if="{{showLoading&&zSlots.loading&&loadingFullFixed}}"><view class="zp-loading-fixed data-v-36d10d5c"><slot name="loading"></slot></view></block></view>