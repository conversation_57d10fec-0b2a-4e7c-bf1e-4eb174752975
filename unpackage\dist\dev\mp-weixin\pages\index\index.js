(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/index/index"],{41:function(e,t,n){"use strict";(function(e,t){var i=n(4);n(26);i(n(25));var r=i(n(42));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n(1)["default"],n(2)["createPage"])},42:function(e,t,n){"use strict";n.r(t);var i=n(43),r=n(45);for(var c in r)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(c);n(56);var o,a=n(32),s=Object(a["default"])(r["default"],i["render"],i["staticRenderFns"],!1,null,null,null,!1,i["components"],o);s.options.__file="pages/index/index.vue",t["default"]=s.exports},43:function(e,t,n){"use strict";n.r(t);var i=n(44);n.d(t,"render",(function(){return i["render"]})),n.d(t,"staticRenderFns",(function(){return i["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return i["recyclableRender"]})),n.d(t,"components",(function(){return i["components"]}))},44:function(e,t,n){"use strict";var i;n.r(t),n.d(t,"render",(function(){return r})),n.d(t,"staticRenderFns",(function(){return o})),n.d(t,"recyclableRender",(function(){return c})),n.d(t,"components",(function(){return i}));try{i={ccNoticeBar:function(){return n.e("uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar").then(n.bind(null,175))},uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,182))},uniList:function(){return n.e("uni_modules/uni-list/components/uni-list/uni-list").then(n.bind(null,190))}}}catch(a){if(-1===a.message.indexOf("Cannot find module")||-1===a.message.indexOf(".vue"))throw a;console.error(a.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var r=function(){var e=this,t=e.$createElement,n=(e._self._c,e.policyLists.length),i=e.activities.length;e.$mp.data=Object.assign({},{$root:{g0:n,g1:i}})},c=!1,o=[];r._withStripped=!0},45:function(e,t,n){"use strict";n.r(t);var i=n(46),r=n.n(i);for(var c in i)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(c);t["default"]=r.a},46:function(e,t,n){"use strict";(function(e){var i=n(4);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=i(n(37)),c=i(n(11)),o=i(n(39)),a=n(47),s=i(n(48));function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,i)}return n}function l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach((function(t){(0,c.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var d=function(){n.e("components/cell").then(function(){return resolve(n(197))}.bind(null,n)).catch(n.oe)},f={data:function(){return{serviceLinks:[{title:"福建人社",icon:n(49),webIdx:0},{title:"福建人才融媒",icon:n(50),webIdx:1},{title:"福建省流动人员档案公共服务平台",icon:n(51),webIdx:5},{title:"福建省职业培训小助手",icon:n(52),webIdx:7},{title:"泉州人才港湾",icon:n(53),webIdx:2},{title:"海峡AI职途",icon:n(54),webIdx:9},{title:"泉就业公共服务平台",icon:n(55),webIdx:4},{title:"泉州就业和人才人事公共服务中心",icon:n(52),webIdx:6}],policyLists:[],activities:[{title:'丰泽区"稳岗扩工"十五条措施10条内容',date:"2023-06-25"},{title:"青年人才集聚行动州州直部门开八条政策措施",date:"2023-06-23"},{title:"丰泽区高校毕业生就业创业政策",date:"2023-07-10"},{title:'丰泽区"稳岗扩工"十五条措施',date:"2023-06-25"}],jyLeiDa:[],noticeBarLists:[],activeTab:0,noticeList:[{id:1,title:"征程这些伟大精神 串连起中国共产党人的精神谱系"},{id:2,title:"增强水运发展新动能 前5月港口货物吞吐量增长7.9%"},{id:3,title:"多地持续高温 各地采取措施积极应对"},{id:4,title:"中非经贸博览会见证中非合作深度"},{id:5,title:"国安家安得民心 保驾护航促治兴"}]}},components:{cellVue:d},mixins:[s.default],methods:{handerTab:function(e){this.activeTab=e,this.fetchInit(e)},jumpService:function(t,n){console.log("item",t);var i="https://ai.enzenith.com/job/#/codePage";e.navigateTo({url:"/bussinessPages/webview/webview?url=".concat(i,"&idx=").concat(t.webIdx,"&tit=").concat(t.title,"&useCode=1")})},fetchInit:function(e){var t=this;return(0,o.default)(r.default.mark((function n(){var i,c;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:n.t0=e,n.next=0===n.t0?3:1===n.t0?10:16;break;case 3:return n.next=5,(0,a.getPolicyLists)({page:1,pageSize:5});case 5:return i=n.sent,console.log("政策列表",i),t.activities=null===i||void 0===i?void 0:i.records.map((function(e){return l(l({},e),{},{title:e.fileName.length>21?e.fileName.substr(0,21)+"...":e.fileName,date:e.createTime})})),t.policyLists=JSON.parse(JSON.stringify(t.activities)),n.abrupt("break",17);case 10:return n.next=12,(0,a.getCompanyLists)({page:1,pageSize:5,firmStatus:0,firmServiceObj:""});case 12:return c=n.sent,console.log("公司列表",c),t.activities=null===c||void 0===c?void 0:c.records.map((function(e){return l(l({},e),{},{title:e.firmName.length>23?e.firmName.substr(0,23)+"...":e.firmName,date:e.createTime})})),n.abrupt("break",17);case 16:return n.abrupt("break",17);case 17:case"end":return n.stop()}}),n)})))()},handlerAction:function(t){var n={jydt:"/bussinessPages/mapPage/mapPage",jyzc:"/bussinessPages/policyPage/policyPage",wyqz:"/bussinessPages/leidaPage/leidaPage"};switch(t){case"jydt":case"wyqz":case"jyzc":e.navigateTo({url:n[t]});break}},jumpAi:function(){var t="https://ai.enzenith.com/job/#/home";e.navigateTo({url:"/bussinessPages/webview/webview?url=".concat(t,"&code=Key_rsj")})},goJumpMore:function(e){var t=this.activeTab;switch(t){case 0:this.handlerAction("jyzc");break;case 1:this.handlerAction("wyqz");break}},goDetailPolicy:function(e){this.handlerOpenPriviewFile(e)},goDetail:function(t,n){var i=this.activeTab;switch(Number(i)){case 0:this.handlerOpenPriviewFile(t);break;case 1:var r="https://ai.enzenith.com/job/#/rsjPolicyLeidaDetail";e.navigateTo({url:"/bussinessPages/webview/webview?url=".concat(r,"&id=").concat(t.id)});break;default:break}}},onLoad:function(){this.fetchInit(0),this.fetchInit(1)}};t.default=f}).call(this,n(2)["default"])},56:function(e,t,n){"use strict";n.r(t);var i=n(57),r=n.n(i);for(var c in i)["default"].indexOf(c)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(c);t["default"]=r.a},57:function(e,t,n){}},[[41,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map