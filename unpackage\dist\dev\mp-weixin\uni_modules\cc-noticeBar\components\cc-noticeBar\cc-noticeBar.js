(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar"],{175:function(n,e,t){"use strict";t.r(e);var r=t(176),o=t(178);for(var c in o)["default"].indexOf(c)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(c);t(180);var i,u=t(32),s=Object(u["default"])(o["default"],r["render"],r["staticRenderFns"],!1,null,"31563cfc",null,!1,r["components"],i);s.options.__file="uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue",e["default"]=s.exports},176:function(n,e,t){"use strict";t.r(e);var r=t(177);t.d(e,"render",(function(){return r["render"]})),t.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]})),t.d(e,"recyclableRender",(function(){return r["recyclableRender"]})),t.d(e,"components",(function(){return r["components"]}))},177:function(n,e,t){"use strict";var r;t.r(e),t.d(e,"render",(function(){return o})),t.d(e,"staticRenderFns",(function(){return i})),t.d(e,"recyclableRender",(function(){return c})),t.d(e,"components",(function(){return r}));try{r={uniIcons:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(t.bind(null,182))}}}catch(u){if(-1===u.message.indexOf("Cannot find module")||-1===u.message.indexOf(".vue"))throw u;console.error(u.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var o=function(){var n=this,e=n.$createElement;n._self._c},c=!1,i=[];o._withStripped=!0},178:function(n,e,t){"use strict";t.r(e);var r=t(179),o=t.n(r);for(var c in r)["default"].indexOf(c)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(c);e["default"]=o.a},179:function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={data:function(){return{}},components:{},props:{colors:{type:String,default:"#333"},noticeList:{type:Array}},methods:{itemClick:function(n){this.$emit("click",n)}}};e.default=r},180:function(n,e,t){"use strict";t.r(e);var r=t(181),o=t.n(r);for(var c in r)["default"].indexOf(c)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(c);e["default"]=o.a},181:function(n,e,t){}}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar-create-component',
    {
        'uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(175))
        })
    },
    [['uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar-create-component']]
]);
