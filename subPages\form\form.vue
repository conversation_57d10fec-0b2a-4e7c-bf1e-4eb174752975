<template>
	<view class="form-container">
		<view class="form-title">表单示例</view>
		
		<uni-forms ref="form" :modelValue="formData" :rules="rules">
			<!-- 基本信息 -->
			<uni-section title="基本信息" type="line">
				<uni-forms-item label="姓名" name="name" required>
					<uni-easyinput v-model="formData.name" placeholder="请输入姓名" />
				</uni-forms-item>
				
				<uni-forms-item label="手机号" name="phone" required>
					<uni-easyinput v-model="formData.phone" placeholder="请输入手机号" />
				</uni-forms-item>
				
				<uni-forms-item label="邮箱" name="email">
					<uni-easyinput v-model="formData.email" placeholder="请输入邮箱" />
				</uni-forms-item>
			</uni-section>
			
			<!-- 选择信息 -->
			<uni-section title="选择信息" type="line">
				<uni-forms-item label="性别" name="gender" required>
					<uni-data-checkbox v-model="formData.gender" :localdata="genderOptions" />
				</uni-forms-item>
				
				<uni-forms-item label="城市" name="city">
					<uni-data-select 
						v-model="formData.city" 
						:localdata="cityOptions" 
						placeholder="请选择城市"
					/>
				</uni-forms-item>
				
				<uni-forms-item label="兴趣爱好" name="hobbies">
					<uni-data-checkbox 
						v-model="formData.hobbies" 
						:localdata="hobbyOptions" 
						multiple
					/>
				</uni-forms-item>
			</uni-section>
			
			<!-- 其他信息 -->
			<uni-section title="其他信息" type="line">
				<uni-forms-item label="个人简介" name="description">
					<uni-easyinput 
						v-model="formData.description" 
						type="textarea" 
						placeholder="请输入个人简介"
						:maxlength="200"
					/>
				</uni-forms-item>
				
				<uni-forms-item label="同意协议" name="agree" required>
					<uni-data-checkbox v-model="formData.agree" :localdata="agreeOptions" />
				</uni-forms-item>
			</uni-section>
		</uni-forms>
		
		<!-- 操作按钮 -->
		<view class="form-actions">
			<button class="btn-submit" @click="submitForm">提交</button>
			<button class="btn-reset" @click="resetForm">重置</button>
		</view>
	</view>
</template>

<script>
import { createData } from '@/api/rs.js'

export default {
	name: 'FormPage',
	data() {
		return {
			formData: {
				name: '',
				phone: '',
				email: '',
				gender: '',
				city: '',
				hobbies: [],
				description: '',
				agree: []
			},
			
			// 表单验证规则
			rules: {
				name: {
					rules: [
						{ required: true, errorMessage: '请输入姓名' },
						{ minLength: 2, maxLength: 10, errorMessage: '姓名长度在2-10个字符' }
					]
				},
				phone: {
					rules: [
						{ required: true, errorMessage: '请输入手机号' },
						{ pattern: /^1[3-9]\d{9}$/, errorMessage: '手机号格式不正确' }
					]
				},
				email: {
					rules: [
						{ format: 'email', errorMessage: '邮箱格式不正确' }
					]
				},
				gender: {
					rules: [
						{ required: true, errorMessage: '请选择性别' }
					]
				},
				agree: {
					rules: [
						{ required: true, errorMessage: '请同意用户协议' }
					]
				}
			},
			
			// 选项数据
			genderOptions: [
				{ value: 'male', text: '男' },
				{ value: 'female', text: '女' }
			],
			
			cityOptions: [
				{ value: 'beijing', text: '北京' },
				{ value: 'shanghai', text: '上海' },
				{ value: 'guangzhou', text: '广州' },
				{ value: 'shenzhen', text: '深圳' }
			],
			
			hobbyOptions: [
				{ value: 'reading', text: '阅读' },
				{ value: 'music', text: '音乐' },
				{ value: 'sports', text: '运动' },
				{ value: 'travel', text: '旅行' }
			],
			
			agreeOptions: [
				{ value: 'agree', text: '我已阅读并同意用户协议' }
			]
		}
	},
	
	methods: {
		// 提交表单
		async submitForm() {
			try {
				// 表单验证
				await this.$refs.form.validate()
				
				// 检查协议
				if (!this.formData.agree.includes('agree')) {
					uni.showToast({
						title: '请同意用户协议',
						icon: 'none'
					})
					return
				}
				
				// 提交数据
				uni.showLoading({ title: '提交中...' })
				
				const result = await createData(this.formData)
				
				uni.hideLoading()
				uni.showToast({
					title: '提交成功',
					icon: 'success'
				})
				
				// 可以跳转到成功页面或返回上一页
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
				
			} catch (error) {
				uni.hideLoading()
				console.error('表单提交失败：', error)
				
				if (error.errorMessage) {
					// 表单验证错误
					uni.showToast({
						title: error.errorMessage,
						icon: 'none'
					})
				} else {
					// 网络或其他错误
					uni.showToast({
						title: '提交失败，请重试',
						icon: 'none'
					})
				}
			}
		},
		
		// 重置表单
		resetForm() {
			this.formData = {
				name: '',
				phone: '',
				email: '',
				gender: '',
				city: '',
				hobbies: [],
				description: '',
				agree: []
			}
			
			// 清除验证状态
			this.$refs.form.clearValidate()
		}
	}
}
</script>

<style lang="scss" scoped>
.form-container {
	padding: 20upx;
	background-color: #f8f8f8;
	min-height: 100vh;
}

.form-title {
	font-size: 32upx;
	font-weight: bold;
	color: #333;
	text-align: center;
	margin-bottom: 40upx;
}

.form-actions {
	margin-top: 60upx;
	padding: 0 40upx;
}

.btn-submit, .btn-reset {
	width: 100%;
	height: 88upx;
	border-radius: 44upx;
	font-size: 32upx;
	margin-bottom: 20upx;
}

.btn-submit {
	background-color: #007AFF;
	color: #ffffff;
}

.btn-reset {
	background-color: #ffffff;
	color: #666666;
	border: 2upx solid #e5e5e5;
}
</style>
