(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/policyPage/policyPage"],{4088:function(e,n,t){"use strict";t.r(n);var a=t("ae6c"),i=t.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(o);n["default"]=i.a},"4eb5":function(e,n,t){"use strict";var a=t("fc47"),i=t.n(a);i.a},"804d":function(e,n,t){"use strict";(function(e,n){var a=t("47a9");t("bfec");a(t("3240"));var i=a(t("c592"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(i.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},ae6c:function(e,n,t){"use strict";var a=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=a(t("45b8")),o=a(t("be70")),c={data:function(){return{body:{areaCodeList:["350503"],isShow:1,state:1,page:1,pageSize:10,total:0}}},mixins:[i.default,o.default],components:{policyCellVue:function(){t.e("bussinessPages/policyPage/policyCell").then(function(){return resolve(t("3656"))}.bind(null,t)).catch(t.oe)}},onLoad:function(){this.fetchList()}};n.default=c},c592:function(e,n,t){"use strict";t.r(n);var a=t("e07e"),i=t("4088");for(var o in i)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(o);t("4eb5");var c=t("828b"),u=Object(c["a"])(i["default"],a["b"],a["c"],!1,null,"3deb4e8e",null,!1,a["a"],void 0);n["default"]=u.exports},e07e:function(e,n,t){"use strict";t.d(n,"b",(function(){return i})),t.d(n,"c",(function(){return o})),t.d(n,"a",(function(){return a}));var a={zPaging:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/z-paging/components/z-paging/z-paging")]).then(t.bind(null,"f225"))}},i=function(){var e=this,n=e.$createElement,t=(e._self._c,e.__map(e.dataList,(function(n,t){var a=e.__get_orig(n),i=Object.assign({},n,{idx:t});return{$orig:a,a0:i}})));e.$mp.data=Object.assign({},{$root:{l0:t}})},o=[]},fc47:function(e,n,t){}},[["804d","common/runtime","common/vendor"]]]);