<template>
	<web-view :src="path" />
</template>

<script>
	export default {
		data() {
			return {
				path: ''
			};
		},
		onLoad(options) {
			console.log("options", options)
			if(!options.useCode) {
				this.path = options.id ? `${options?.url}?id=${options.id}` : `${options?.url}?code=${options.code}`
			}else {
				this.path = `${options.url}?idx=${options.idx}&tit=${options.tit}`
			}
			//设置标题
			// if(options.tit) {
			// 	uni.setNavigationBarTitle({
			// 		title: options.tit
			// 	});
			// }
		}
	}
</script>

<style lang="scss">

</style>