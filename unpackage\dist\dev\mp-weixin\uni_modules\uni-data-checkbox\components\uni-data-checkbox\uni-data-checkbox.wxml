<view class="uni-data-checklist" style="{{'margin-top:'+(isTop+'px')+';'}}"><block wx:if="{{!isLocal}}"><view class="uni-data-loading"><block wx:if="{{!mixinDatacomErrorMessage}}"><uni-load-more vue-id="15e4571e-1" status="loading" iconType="snow" iconSize="{{18}}" content-text="{{contentText}}" bind:__l="__l"></uni-load-more></block><block wx:else><text>{{mixinDatacomErrorMessage}}</text></block></view></block><block wx:else><block wx:if="{{multiple}}"><checkbox-group data-event-opts="{{[['change',[['change',['$event']]]]]}}" class="{{['checklist-group',(mode==='list'||wrap)?'is-list':'']}}" bindchange="__e"><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><label class="{{['checklist-box','is--'+mode,item.selected?'is-checked':'',disabled||!!item.disabled?'is-disable':'',index!==0&&mode==='list'?'is-list-border':'']}}" style="{{(item.styleBackgroud)}}"><checkbox class="hidden" hidden="{{true}}" disabled="{{disabled||!!item.disabled}}" value="{{item[map.value]+''}}" checked="{{item.selected}}"></checkbox><block wx:if="{{mode!=='tag'&&mode!=='list'||mode==='list'&&icon==='left'}}"><view class="checkbox__inner" style="{{(item.styleIcon)}}"><view class="checkbox__inner-icon"></view></view></block><view class="{{['checklist-content',(mode==='list'&&icon==='left')?'list-content':'']}}"><text class="checklist-text" style="{{(item.styleIconText)}}">{{item[map.text]}}</text><block wx:if="{{mode==='list'&&icon==='right'}}"><view class="checkobx__list" style="{{(item.styleBackgroud)}}"></view></block></view></label></block></checkbox-group></block><block wx:else><radio-group data-event-opts="{{[['change',[['change',['$event']]]]]}}" class="{{['checklist-group',(mode==='list')?'is-list':'',(wrap)?'is-wrap':'']}}" bindchange="__e"><block wx:for="{{dataList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><label class="{{['checklist-box','is--'+mode,item.selected?'is-checked':'',disabled||!!item.disabled?'is-disable':'',index!==0&&mode==='list'?'is-list-border':'']}}" style="{{(item.styleBackgroud)}}"><radio class="hidden" hidden="{{true}}" disabled="{{disabled||item.disabled}}" value="{{item[map.value]+''}}" checked="{{item.selected}}"></radio><block wx:if="{{mode!=='tag'&&mode!=='list'||mode==='list'&&icon==='left'}}"><view class="radio__inner" style="{{(item.styleBackgroud)}}"><view class="radio__inner-icon" style="{{(item.styleIcon)}}"></view></view></block><view class="{{['checklist-content',(mode==='list'&&icon==='left')?'list-content':'']}}"><text class="checklist-text" style="{{(item.styleIconText)}}">{{item[map.text]}}</text><block wx:if="{{mode==='list'&&icon==='right'}}"><view class="checkobx__list" style="{{(item.styleRightIcon)}}"></view></block></view></label></block></radio-group></block></block></view>