{
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/index/index",
			"style": {
				"navigationBarTitleText": "首页",
				"transparentTitle": "always",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/mine/mine",
			"style": {
				"navigationBarTitleText": "",
				"transparentTitle": "always",
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [{
		"root": "bussinessPages",
		"pages": [
			{
				"path": "webview/webview",
				"style": {
					"navigationBarTitleText": "",
					"transparentTitle": "always"
				}
			},
			{
				"path": "mapPage/mapPage",
				"style": {
					"navigationBarTitleText": "就业地图"
				}
			},
			{
				"path": "policyPage/policyPage",
				"style": {
					"navigationBarTitleText": "就业政策",
					"transparentTitle": "always"
				}
			},
			{
				"path": "updateMine/updateMine",
				"style": {
					"navigationBarTitleText": "个人信息"
				}
			},
			{
				"path": "tjForm/tjForm",
				"style": {
					"navigationBarTitleText": "提交意向"
				}
			},
			{
				"path" : "setting/setting",
				"style" : 
				{
					"navigationBarTitleText" : "设置"
				}
			},
			{
				"path" : "leidaPage/leidaPage",
				"style" : 
				{
					"navigationBarTitleText" : "就业雷达"
				}
			},
			{
				"path" : "gwDetail/gwDetail",
				"style" : 
				{
					"navigationBarTitleText" : "岗位详情"
				}
			},
			{
				"path" : "qrCodePage/qrCodePage",
				"style" : 
				{
					"navigationBarTitleText" : ""
				}
			},
			{
				"path" : "pldDetail/pldDetail",
				"style" : 
				{
					"navigationBarTitleText" : "就业雷达详情"
				}
			},
			{
				"path" : "tjRecordPages/tjRecordPages",
				"style" : 
				{
					"navigationBarTitleText" : "提交记录"
				}
			},
			{
				"path" : "tjRecordDetail/tjRecordDetail",
				"style" : 
				{
					"navigationBarTitleText" : "提交意向"
				}
			}
		]
	}],
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-app",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"tabBar": {
		"color": "#7A8499",
		"selectedColor": "#0080FF",
		"borderStyle": "black",
		"backgroundColor": "#FFFFFF",
		"fontSize": "26upx",
		"iconWidth": "30px",
		"height": "60px",
		"list": [{
			"pagePath": "pages/index/index",
			"iconPath": "static/tabar/tab-0.png",
			"selectedIconPath": "static/tabar/tab-1.png",
			"text": "首页"
		}, {
			"pagePath": "pages/mine/mine",
			"iconPath": "static/tabar/tab-1-0.png",
			"selectedIconPath": "static/tabar/tab-1-1.png",
			"text": "我的"
		}]
	},
	"uniIdRouter": {}
}