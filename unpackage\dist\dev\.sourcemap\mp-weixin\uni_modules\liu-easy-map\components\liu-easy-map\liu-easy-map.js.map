{"version": 3, "sources": ["webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue?78ed", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue?ee5e", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue?3bdc", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue?1f4d", "uni-app:///uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue?0b9e"], "names": ["renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "props", "centerLat", "type", "default", "centerLng", "markerData", "polygons", "markerIconWidth", "markerIconHeight", "markerIconUrl", "scale", "isShowCompass", "isEnableZoom", "isEnableScroll", "isEnableRotate", "goImgIn", "markerImgIn", "closeIcon", "watch", "immediate", "deep", "handler", "data", "markerImg", "goImg", "myaddressImg", "wxmapImg", "myaddressOnImg", "wxmapOnImg", "closeImg", "polygonsData", "markers", "detailData", "nowLat", "nowLng", "tabIndex", "tabIndex2", "isShowWxMap", "isShowDetail", "wxMapShow", "mounted", "methods", "changeTab", "getLocation", "uni", "isHighAccuracy", "highAccuracyExpireTime", "success", "console", "id", "latitude", "longitude", "width", "height", "iconPath", "mapObjs", "complete", "fail", "content", "confirmText", "cancelText", "goRoute", "name", "address", "clear<PERSON><PERSON><PERSON>", "showMarkers", "arr", "rotate", "callout", "color", "fontSize", "borderRadius", "padding", "bgColor", "display", "chooseItem", "clickMap", "closeDetail"], "mappings": "8JAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,oEACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAAmyB,eAAG,G,4ICmCtyB,CACAC,OAEAC,WACAC,qBACAC,YAGAC,WACAF,qBACAC,YAGAE,YACAH,WACAC,mBACA,WAIAG,UACAJ,WACAC,mBACA,WAIAI,iBACAL,YACAC,YAGAK,kBACAN,YACAC,YAGAM,eACAP,YACAC,YAGAO,OACAR,YACAC,YAGAQ,eACAT,aACAC,YAGAS,cACAV,aACAC,YAGAU,gBACAX,aACAC,YAGAW,gBACAZ,aACAC,YAEAY,WACAC,eACAC,cAEAC,OACAb,YACAc,aACAC,QACAC,sBACA,mBACA,qBAGAL,aACAK,sBAKAF,aACAC,SAEAL,SACAM,sBAKAF,aACAC,SAEAH,WACAI,sBAKAF,aACAC,SAEAd,UACAa,aACAC,QACAC,sBACA,sCAIAC,gBACA,OACAC,oCACAC,aACAC,oBACAC,gBACAC,sBACAC,kBACAC,gBACAC,gBACAC,WACAC,cACAC,UACAC,UACAC,YACAC,aACAC,eACAC,gBACAC,eAGAC,mBACA,wCACA,iBACA,mBAEA,mBACA,qCAEAC,SAEAC,sBACA,MACA,6BACA,iCACA,qBAEA,+BACA,mCACA,sBAIAC,uBAAA,WACAC,eACA1C,aACA2C,kBACAC,4BACAC,oBACAC,sBACA,oBACA,qBACA,QACAC,QACAC,wBACAC,0BACAC,wBACAC,0BACAC,uBAEA,uBACA,sCACAC,kBACAL,oBACAC,uBACA,CACAK,0BAGAC,iBACA,wCACAb,aACAc,mCACAC,iBACAC,gBACAb,oBACA,cAKA,SAJAH,eACAG,+BAYAc,mBACAjB,gBACAM,mCACAC,qCACAzC,SACAoD,gCACAC,yCAIAC,uBACA,iBAGAC,uBACA,gDAEA,IADA,SACA,kCACAC,QACAjB,kCACAC,0CACAC,4CACAG,0EACA/B,UACA4C,SACAf,uEACA7C,gBACA8C,0EACA7C,iBACA4D,SACAV,iCACAW,kDACAC,iDACAC,wDACAC,8CACAC,sDACAC,yDAIA,iBAIAC,uBAEA,IADA,wBACA,kCACA,8BACA,qBACA,oCACA,8CACA,QAKAC,qBAEA,mCACA,gCACA,uBACA1B,WACAC,eAKA0B,uBACA,mBACA,wBAGA,c,6DCvTA,yHAA0nC,eAAG,G", "file": "uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./liu-easy-map.vue?vue&type=template&id=8276d06c&\"\nvar renderjs\nimport script from \"./liu-easy-map.vue?vue&type=script&lang=js&\"\nexport * from \"./liu-easy-map.vue?vue&type=script&lang=js&\"\nimport style0 from \"./liu-easy-map.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-easy-map.vue?vue&type=template&id=8276d06c&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-easy-map.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-easy-map.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"width: 100%; height: 100%;\">\r\n\t\t<!-- nowLat ? nowLat : -->\r\n\t\t<!-- nowLng ? nowLng : -->\r\n\t\t<map style=\"width: 100%; height: 100%;\" id=\"esaymap\" :scale=\"scale\" :latitude=\"centerLat\"\r\n\t\t\t:longitude=\"centerLng\" :markers=\"markers\" :polygons=\"polygonsData\"\r\n\t\t\t:enable-zoom=\"isEnableZoom\" :enable-scroll=\"isEnableScroll\" :enable-satellite=\"isShowWxMap\"\r\n\t\t\t:enable-rotate=\"isEnableRotate\" @markertap=\"chooseItem\" @tap=\"clickMap\" @regionchange=\"$emit('regionchange')\">\r\n\t\t</map>\r\n\t\t<view class=\"rightbox\" v-show=\"false\">\r\n\t\t\t<view class=\"boxitem\" @click=\"changeTab(1)\">\r\n\t\t\t\t<image class=\"itemimg\" :src=\"tabIndex ? myaddressOnImg : myaddressImg\" mode=\"\"></image>\r\n\t\t\t\t<view class=\"itemname\" :class=\"tabIndex ? 'active' : ''\">我的位置</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"boxitem\" @click=\"changeTab(2)\" v-if=\"wxMapShow\">\r\n\t\t\t\t<image class=\"itemimg\" :src=\"tabIndex2 ? wxmapOnImg:wxmapImg\" mode=\"\"></image>\r\n\t\t\t\t<view class=\"itemname\" :class=\"tabIndex2 ? 'active' : ''\">卫星地图</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<cover-view class=\"detailbox\" v-if=\"isShowDetail\">\r\n\t\t\t<!-- <cover-image class=\"closeicon\" :src=\"closeImg\" @click=\"closeDetail\">关闭</cover-image> -->\r\n\t\t\t<cover-view class=\"closeicon\" @click=\"closeDetail\">关闭</cover-view>\r\n\t\t\t<cover-view class=\"boxl\">\r\n\t\t\t\t<cover-view class=\"boxlhd ellipsis\">{{detailData.name || '--'}}</cover-view>\r\n\t\t\t\t<cover-view class=\"boxlbd ellipsis\">{{detailData.address || '--'}}</cover-view>\r\n\t\t\t</cover-view>\r\n\t\t\t<cover-view class=\"boxr\" @click=\"goRoute\">去导航</cover-view>\r\n<!-- \t\t\t<cover-view class=\"boxr\" @click=\"goRoute\">\r\n\t\t\t\t<cover-image class=\"boxrimg\" :src=\"goImg\" mode=\"\"></cover-image>\r\n\t\t\t</cover-view> -->\r\n\t\t</cover-view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\t//中心点纬度\r\n\t\t\tcenterLat: {\r\n\t\t\t\ttype: [String,Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t//中心点经度\r\n\t\t\tcenterLng: {\r\n\t\t\t\ttype: [String,Number],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t//标记点数据\r\n\t\t\tmarkerData: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//多边形数据\r\n\t\t\tpolygons: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//标记点图标宽度\r\n\t\t\tmarkerIconWidth: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 22\r\n\t\t\t},\r\n\t\t\t//标记点图标高度\r\n\t\t\tmarkerIconHeight: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 32\r\n\t\t\t},\r\n\t\t\t//标记点图标路径\r\n\t\t\tmarkerIconUrl: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t//缩放级别 取值范围为3-20\r\n\t\t\tscale: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 16\r\n\t\t\t},\r\n\t\t\t//是否显示指南针\r\n\t\t\tisShowCompass: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t//是否支持缩放\r\n\t\t\tisEnableZoom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t//是否支持拖动\r\n\t\t\tisEnableScroll: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t//是否支持旋转\r\n\t\t\tisEnableRotate: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tgoImgIn: '',\r\n\t\t\tmarkerImgIn: '',\r\n\t\t\tcloseIcon: ''\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tmarkerData: {\r\n\t\t\t\timmediate: true, //初始化的时候是否调用\r\n\t\t\t\tdeep: true, //是否开启深度监听\r\n\t\t\t\thandler(newValue, oldValue) {\r\n\t\t\t\t\tthis.markerDatas = newValue\r\n\t\t\t\t\tthis.showMarkers()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tmarkerImgIn: {\r\n\t\t\t\thandler(val) {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.markerImg = val\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true,\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t\tgoImgIn: {\r\n\t\t\t\thandler(val) {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.goImg = val\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true,\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t\tcloseIcon: {\r\n\t\t\t\thandler(val) {\r\n\t\t\t\t\t// #ifdef H5\r\n\t\t\t\t\tthis.closeImg = val\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t},\r\n\t\t\t\timmediate: true,\r\n\t\t\t\tdeep: true\r\n\t\t\t},\r\n\t\t\tpolygons: {\r\n\t\t\t\timmediate: true, //初始化的时候是否调用\r\n\t\t\t\tdeep: true, //是否开启深度监听\r\n\t\t\t\thandler(newValue, oldValue) {\r\n\t\t\t\t\tthis.polygonsData = [...newValue]\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmarkerImg: '../../static/marker.png',\r\n\t\t\t\tgoImg: require('../../static/go.png'),\r\n\t\t\t\tmyaddressImg: require('../../static/myaddress.png'),\r\n\t\t\t\twxmapImg: require('../../static/wxmap.png'),\r\n\t\t\t\tmyaddressOnImg: require('../../static/myaddress-on.png'),\r\n\t\t\t\twxmapOnImg: require('../../static/wxmap-on.png'),\r\n\t\t\t\tcloseImg: require('../../static/close.png'),\r\n\t\t\t\tpolygonsData: [], //polygons区域数据\r\n\t\t\t\tmarkers: [], //markers数据\r\n\t\t\t\tdetailData: {}, //选中展示详情数据\r\n\t\t\t\tnowLat: '', //我的当前位置\r\n\t\t\t\tnowLng: '',\r\n\t\t\t\ttabIndex: false,\r\n\t\t\t\ttabIndex2: false,\r\n\t\t\t\tisShowWxMap: false, //是否展示卫星地图 \r\n\t\t\t\tisShowDetail: false, //是否展示详情弹框\r\n\t\t\t\twxMapShow: false, //是否展示卫星地图按钮（小程序展示）\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tconst type = uni.getSystemInfoSync().uniPlatform\r\n\t\t\tif (type == 'mp-weixin') {\r\n\t\t\t\tthis.wxMapShow = true\r\n\t\t\t}\r\n\t\t\tthis.showMarkers()\r\n\t\t\tif (!this.markerData) this.getLocation()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//右侧类型切换\r\n\t\t\tchangeTab(index) {\r\n\t\t\t\tif (index == 1) {\r\n\t\t\t\t\tthis.tabIndex = !this.tabIndex\r\n\t\t\t\t\tif (this.tabIndex) this.getLocation()\r\n\t\t\t\t\telse this.showMarkers()\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.tabIndex2 = !this.tabIndex2\r\n\t\t\t\t\tif (this.tabIndex2) this.isShowWxMap = true\r\n\t\t\t\t\telse this.isShowWxMap = false\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//获取当前的地理位置\r\n\t\t\tgetLocation() {\r\n\t\t\t\tuni.getLocation({\r\n\t\t\t\t\ttype: 'gcj02',\r\n\t\t\t\t\tisHighAccuracy: true,\r\n\t\t\t\t\thighAccuracyExpireTime: 3500,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconsole.log(\"获取地址\", res)\r\n\t\t\t\t\t\tthis.nowLat = res.latitude\r\n\t\t\t\t\t\tthis.nowLng = res.longitude\r\n\t\t\t\t\t\tlet arr = [{\r\n\t\t\t\t\t\t\tid: 9999,\r\n\t\t\t\t\t\t\tlatitude: res.latitude || '', //纬度\r\n\t\t\t\t\t\t\tlongitude: res.longitude || '', //经度\r\n\t\t\t\t\t\t\twidth: this.markerIconWidth, //宽\r\n\t\t\t\t\t\t\theight: this.markerIconHeight, //高\r\n\t\t\t\t\t\t\ticonPath: this.markerImg\r\n\t\t\t\t\t\t}];\r\n\t\t\t\t\t\tthis.markers = [...arr];\r\n\t\t\t\t\t\tlet mapObjs = uni.createMapContext('esaymap', this)\r\n\t\t\t\t\t\tmapObjs.moveToLocation({\r\n\t\t\t\t\t\t\tlatitude: res.latitude,\r\n\t\t\t\t\t\t\tlongitude: res.longitude\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\tcomplete: res => {}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t},\r\n\t\t\t\t\tfail: (res) => {\r\n\t\t\t\t\t\tif (res.errMsg == \"getLocation:fail auth deny\") {\r\n\t\t\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\t\t\tcontent: '检测到您没打开获取信息功能权限，是否去设置打开？',\r\n\t\t\t\t\t\t\t\tconfirmText: \"确认\",\r\n\t\t\t\t\t\t\t\tcancelText: '取消',\r\n\t\t\t\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\t\t\t\tif (res.confirm) {\r\n\t\t\t\t\t\t\t\t\t\tuni.openSetting({\r\n\t\t\t\t\t\t\t\t\t\t\tsuccess: (res) => {}\r\n\t\t\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\t\t\treturn false;\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//到这去\r\n\t\t\tgoRoute() {\r\n\t\t\t\tuni.openLocation({\r\n\t\t\t\t\tlatitude: +this.detailData.latitude,\r\n\t\t\t\t\tlongitude: +this.detailData.longitude,\r\n\t\t\t\t\tscale: 17,\r\n\t\t\t\t\tname: this.detailData.name || '--',\r\n\t\t\t\t\taddress: this.detailData.address || '--'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 移除marker\r\n\t\t\tclearMarker(){\r\n\t\t\t\tthis.markers = []\r\n\t\t\t},\r\n\t\t\t//地图打点展示marker\r\n\t\t\tshowMarkers() {\r\n\t\t\t\tif (this.markerDatas && this.markerDatas.length > 0) {\r\n\t\t\t\t\tvar arr = []\r\n\t\t\t\t\tfor (var i = 0; i < this.markerDatas.length; i++) {\r\n\t\t\t\t\t\tarr.push({\r\n\t\t\t\t\t\t\tid: Number(this.markerDatas[i].id),\r\n\t\t\t\t\t\t\tlatitude: this.markerDatas[i].latitude || '', //纬度\r\n\t\t\t\t\t\t\tlongitude: this.markerDatas[i].longitude || '', //经度\r\n\t\t\t\t\t\t\ticonPath: this.markerDatas[i].markerUrl ? this.markerDatas[i].markerUrl : this\r\n\t\t\t\t\t\t\t\t.markerImg, //显示的图标        \r\n\t\t\t\t\t\t\trotate: 0, // 旋转度数\r\n\t\t\t\t\t\t\twidth: this.markerDatas[i].iconWidth ? this.markerDatas[i].iconWidth : this\r\n\t\t\t\t\t\t\t\t.markerIconWidth, //宽\r\n\t\t\t\t\t\t\theight: this.markerDatas[i].iconHeight ? this.markerDatas[i].iconHeight : this\r\n\t\t\t\t\t\t\t\t.markerIconHeight, //高\r\n\t\t\t\t\t\t\tcallout: { //自定义标记点上方的气泡窗口 点击有效\r\n\t\t\t\t\t\t\t\tcontent: this.markerDatas[i].name, //文本\r\n\t\t\t\t\t\t\t\tcolor: this.markerDatas[i].calloutColor || '#ffffff', //文字颜色\r\n\t\t\t\t\t\t\t\tfontSize: this.markerDatas[i].calloutFontSize || 14, //文本大小\r\n\t\t\t\t\t\t\t\tborderRadius: this.markerDatas[i].calloutBorderRadius || 6, //边框圆角\r\n\t\t\t\t\t\t\t\tpadding: this.markerDatas[i].calloutPadding || 6,\r\n\t\t\t\t\t\t\t\tbgColor: this.markerDatas[i].calloutBgColor || '#0B6CFF', //背景颜色\r\n\t\t\t\t\t\t\t\tdisplay: this.markerDatas[i].calloutDisplay || 'BYCLICK', //常显\r\n\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.markers = arr\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//点击标记点\r\n\t\t\tchooseItem(e) {\r\n\t\t\t\tlet markerId = e.detail.markerId\r\n\t\t\t\tfor (var i = 0; i < this.markerDatas.length; i++) {\r\n\t\t\t\t\tif (this.markerDatas[i].id == markerId) {\r\n\t\t\t\t\t\tthis.isShowDetail = true\r\n\t\t\t\t\t\tthis.detailData = this.markerDatas[i]\r\n\t\t\t\t\t\tthis.$emit(\"clickMarker\", this.markerDatas[i])\r\n\t\t\t\t\t\tbreak\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t//点击地图(仅微信小程序支持)\r\n\t\t\tclickMap(e) {\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\tlet lat = e.detail.latitude.toFixed(5)\r\n\t\t\t\tlet lng = e.detail.longitude.toFixed(5)\r\n\t\t\t\tthis.$emit(\"clickMap\", {\r\n\t\t\t\t\tlatitude: lat,\r\n\t\t\t\t\tlongitude: lng\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t//关闭详情弹框\r\n\t\t\tcloseDetail() {\r\n\t\t\t\tthis.detailData = {}\r\n\t\t\t\tthis.isShowDetail = false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.rightbox {\r\n\t\tpadding: 0 8upx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-shadow: 0upx 4upx 8upx 0upx rgba(200, 200, 200, 0.5);\r\n\t\tborder-radius: 14upx;\r\n\t\tposition: fixed;\r\n\t\ttop: 154upx;\r\n\t\tright: 20upx;\r\n\t}\r\n\r\n\t.boxitem {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\ttext-align: center;\r\n\t\tpadding-bottom: 8upx;\r\n\t\tborder-bottom: 2upx solid #E4E4E4;\r\n\t}\r\n\r\n\t.itemimg {\r\n\t\twidth: 40upx;\r\n\t\theight: 40upx;\r\n\t\tmargin: 16upx auto 4upx;\r\n\t}\r\n\r\n\t.itemname {\r\n\t\tfont-size: 22upx;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #333333;\r\n\t\tline-height: 42upx;\r\n\t}\r\n\r\n\t.active {\r\n\t\tcolor: #2765F1;\r\n\t}\r\n\r\n\t.detailbox {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\twidth: calc(100% - 128upx);\r\n\t\tpadding: 36upx 32upx;\r\n\t\tbackground: #FFFFFF;\r\n\t\tborder-radius: 16upx;\r\n\t\tposition: fixed;\r\n\t\tbottom: 32upx;\r\n\t\tleft: 32upx;\r\n\t}\r\n\r\n\t.closeicon {\r\n\t\twidth: 80upx;\r\n\t\theight: 40upx;\r\n\t\tposition: absolute;\r\n\t\tright: 16upx;\r\n\t\ttop: 12upx;\r\n\t\t/* color: #999 */\r\n\t}\r\n\r\n\t.boxl {\r\n\t\twidth: calc(100% - 84upx);\r\n\t}\r\n\r\n\t.boxlhd {\r\n\t\tmargin-bottom: 16upx;\r\n\t\twhite-space: pre-wrap;\r\n\t\tfont-size: 36upx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\tline-height: 48upx;\r\n\t}\r\n\r\n\t.boxlbd {\r\n\t\tfont-size: 30upx;\r\n\t\tfont-weight: 400;\r\n\t\tcolor: #333333;\r\n\t\tline-height: 46upx;\r\n\t\twhite-space: pre-wrap;\r\n\t}\r\n\r\n\t.boxr {\r\n\t\twidth: 96upx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tposition: absolute;\r\n\t\tbottom: 44upx;\r\n\t\tright: 16upx;\r\n\t\tcolor: #999\r\n\t}\r\n\r\n/* \t.boxr::before {\r\n\t\twidth: 2upx;\r\n\t\theight: 96upx;\r\n\t\tbackground: #e3e3e3;\r\n\t\tcontent: \"\";\r\n\t\tposition: relative;\r\n\t\tleft: 0;\r\n\t\tz-index: 99;\r\n\t} */\r\n\r\n\t.boxrimg {\r\n\t\twidth: 64upx;\r\n\t\theight: 64upx;\r\n\t\tmargin: 0 auto;\r\n\t}\r\n\t.ellipsis {\r\n\t\twhite-space: nowrap;\r\n\t\t/* 不换行 */\r\n\t\toverflow: hidden;\r\n\t\t/* 超出部分隐藏 */\r\n\t\ttext-overflow: ellipsis;\r\n\t\twidth: 100% !important;\r\n\t\t/* 超出部分显示省略号 */\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-easy-map.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./liu-easy-map.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}