<view data-event-opts="{{[['tap',[['$emit',['onDetail','$0'],['obj']]]]]}}" class="company-cell data-v-40b31440" bindtap="__e"><view class="header-cell data-v-40b31440"><view class="company-title over-text data-v-40b31440">{{obj.firmManagementName}}</view><view class="detail-link data-v-40b31440">详情<uni-icons vue-id="32f319b3-1" type="right" color="#999999" size="16" class="data-v-40b31440" bind:__l="__l"></uni-icons></view></view><view class="company-info data-v-40b31440"><view class="info-item data-v-40b31440"><text class="over-text float-tit data-v-40b31440">所属街道：</text><text class="over-text float-value data-v-40b31440">{{obj.firmStreet||'-'}}</text></view><view class="info-item data-v-40b31440"><text class="over-text float-tit data-v-40b31440">求职意向：</text><text class="over-text float-value data-v-40b31440">{{obj.firmManagementPostName||'-'}}</text></view><view class="info-item data-v-40b31440"><text class="over-text float-tit data-v-40b31440">提交时间：</text><text class="over-text float-value data-v-40b31440">{{obj.createTime||'-'}}</text></view></view></view>