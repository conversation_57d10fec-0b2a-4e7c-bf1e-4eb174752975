{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/pages/index/index.vue?dc8c", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/pages/index/index.vue?778c", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/pages/index/index.vue?6c03", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/pages/index/index.vue?2d87", "uni-app:///pages/index/index.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/pages/index/index.vue?b214"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "ccNoticeBar", "uniIcons", "uniList", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "g0", "_self", "_c", "policyLists", "length", "g1", "activities", "$mp", "data", "Object", "assign", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "serviceLinks", "title", "icon", "webIdx", "date", "jyLeiDa", "noticeBarLists", "activeTab", "noticeList", "id", "cellVue", "mixins", "methods", "handerTab", "jumpService", "uni", "url", "fetchInit", "type", "page", "pageSize", "res", "el", "fileName", "firmStatus", "firmServiceObj", "firmName", "handlerAction", "j<PERSON>t", "jyzc", "wyqz", "jumpAi", "goJumpMore", "goDetailPolicy", "goDetail", "onLoad"], "mappings": "2IAAA,MAGA,aACA,WAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,+ECLX,iIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,wBACZ,aAAAF,E,yCCvBf,sQ,gCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,YAAa,WACX,OAAO,6FAITC,SAAU,WACR,OAAO,wHAITC,QAAS,WACP,OAAO,kFAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,YAAYC,QACrBC,EAAKT,EAAIU,WAAWF,OACxBR,EAAIW,IAAIC,KAAOC,OAAOC,OACpB,GACA,CACEC,MAAO,CACLX,GAAIA,EACJK,GAAIA,MAKRO,GAAmB,EACnBC,EAAkB,GACtBlB,EAAOmB,eAAgB,G,gCCtDvB,wHAA8vB,eAAG,G,+JC6HjwB,QAKA,msBACA,CACAN,gBACA,OACAO,eACAC,aACAC,WACAC,UAEA,CACAF,eACAC,WACAC,UAEA,CACAF,wBACAC,WACAC,UAEA,CACAF,mBACAC,WACAC,UAEA,CACAF,eACAC,WACAC,UAOA,CACAF,eACAC,WACAC,UAEA,CACAF,kBACAC,WACAC,UAEA,CACAF,wBACAC,WACAC,WAGAf,eACAG,aACAU,4BACAG,mBAEA,CACAH,6BACAG,mBAEA,CACAH,uBACAG,mBAEA,CACAH,uBACAG,oBAGAC,WACAC,kBACAC,YACAC,aACAC,KACAR,iCAEA,CACAQ,KACAR,oCAEA,CACAQ,KACAR,2BAEA,CACAQ,KACAR,yBAEA,CACAQ,KACAR,4BAKA9B,YACAuC,WAEAC,mBACAC,SACAC,sBACA,iBACA,mBAEAC,0BACApC,sBACA,+CACAqC,cACAC,sHAIAC,sBAAA,iJACAC,EAAA,OACA,IADA,KACA,EAiBA,IAjBA,KAiBA,oCAhBA,qBACAC,OACAC,aACA,OAUA,OAbAC,SAIA3C,sBACA,oEACA,cACA4C,OACArB,2DACAsB,SACAnB,uBAGA,sGAIA,sBACAe,OACAC,WACAI,aACAC,oBACA,QAUA,OAfAJ,SAMA3C,sBAEA,oEACA,cACA4C,OACArB,2DACAyB,SACAtB,uBAEA,oGAnCA,IA0CAuB,0BAGA,IAWA,GACAC,uCACAC,6CACAC,4CAGA,UACA,WACA,WACA,WACAf,cACAC,WAGA,QASAe,kBACA,2CAEAhB,cACAC,wEAGAgB,uBACA,IACAzB,EACA,KADAA,UAEA,UACA,OACA,2BAEA,MACA,OACA,2BAEA,QAGA0B,2BACA,gCAEAC,uBACA,IACA3B,EACA,KADAA,UAEA,kBACA,OACA,+BAEA,MACA,OACA,2DACAQ,cACAC,2EAGA,MACA,QACA,SAIAmB,kBACA,kBACA,oBAEA,c,4DClXA,wHAAy5C,eAAG,G", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    ccNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar\" */ \"@/uni_modules/cc-noticeBar/components/cc-noticeBar/cc-noticeBar.vue\"\n      )\n    },\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n    uniList: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-list/components/uni-list/uni-list\" */ \"@/uni_modules/uni-list/components/uni-list/uni-list.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.policyLists.length\n  var g1 = _vm.activities.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"dash-container custom-nav\">\r\n\t\t<!-- 顶部城市风景图和标题 -->\r\n\t\t<view class=\"top-tips\">本小程序已接入Deepseek智能服务</view>\r\n\t\t<view class=\"top-banner\">\r\n\t\t\t<swiper class=\"top-img\" :indicator-dots=\"true\" indicator-active-color=\"#fff\" circular :autoplay=\"true\" :interval=\"3000\"\r\n\t\t\t\t:duration=\"500\">\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<image class=\"top-img\" src=\"https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914856679430684672.png\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<image class=\"top-img\" src=\"https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914856855847305216.png\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<image class=\"top-img\" src=\"https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914857044343521280.png\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<image class=\"top-img\" src=\"https://bmhqpt.qzdsj.net/eos/policy/20250423/image/1914857249231077376.png\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<image class=\"top-img\" src=\"https://bmhqpt.qzdsj.net/eos/policy/20250424/image/1915331732400242688.png\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t\t<swiper-item>\r\n\t\t\t\t\t<image class=\"top-img\" src=\"https://bmhqpt.qzdsj.net/eos/policy/20250424/image/1915331947240882176.png\"></image>\r\n\t\t\t\t</swiper-item>\r\n\t\t\t</swiper>\r\n\t\t</view>\r\n\t\t<!-- ai助手 -->\r\n\t\t<image @click=\"jumpAi\" src=\"https://bmhqpt.qzdsj.net/eos/policy/20250425/image/1915567393153744896.png\" class=\"ai-box\" mode=\"\"></image>\r\n\t\t<!-- 轮播通知 -->\r\n\t\t<view class=\"notice-swiper\">\r\n\t\t\t<cc-noticeBar v-if=\"policyLists.length\" :noticeList=\"policyLists\" colors=\"#000000\" @click=\"goDetailPolicy\">\r\n\t\t\t\t<view slot=\"left-icon\">\r\n\t\t\t\t\t<uni-icons type=\"sound-filled\" color=\"#FFAE00\" size=\"30\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!-- \t\t\t\t<view slot=\"right-btn\">\r\n\t\t\t\t\t<view class=\"detail-txt\">详情<uni-icons type=\"right\" color=\"#FFAE00\" size=\"17\"></uni-icons></view>\r\n\t\t\t\t</view> -->\r\n\t\t\t</cc-noticeBar>\r\n\t\t</view>\r\n\r\n\t\t<!-- 功能区域 -->\r\n\t\t<view class=\"function-area\">\r\n\t\t\t<view class=\"grid-item-box\" @click=\"handlerAction('jydt')\">\r\n\t\t\t\t<image class=\"jyzc-img\" style=\"border-radius: 22upx;\" src=\"../../static/bac/jydt.png\" mode=\"aspectFit\">\r\n\t\t\t\t</image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"grid-item-box\" @click=\"handlerAction('wyqz')\">\r\n\t\t\t\t<image class=\"jyzc-img\" style=\"border-radius: 22upx;\" src=\"../../static/bac/wyqz.png\" mode=\"aspectFit\">\r\n\t\t\t\t</image>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"jyzc-box\" @click=\"handlerAction('jyzc')\">\r\n\t\t\t<image class=\"jyzc-img\" style=\"border-radius: 60upx;\" src=\"../../static/bac/jyzc.png\" mode=\"widthFix\">\r\n\t\t\t</image>\r\n\t\t</view>\r\n\t\t<!-- 就业服务链接区域 -->\r\n\t\t<view class=\"service-links\">\r\n\t\t\t<view class=\"service-title\"></view>\r\n\t\t\t<view class=\"service-box\">\r\n\t\t\t\t<!-- :class=\"[item.title.length > 6 ? 'service-item-cell' :'service-item']\" -->\r\n\t\t\t\t<view class=\"service-item\" v-for=\"(item, index) in serviceLinks\" :key=\"index\"\r\n\t\t\t\t\t@click=\"jumpService(item,index)\">\r\n\t\t\t\t\t<image class=\"icon-service\" :src=\"item.icon\" mode=\"\"></image>\r\n\t\t\t\t\t<view class=\"font-service\">{{item.title}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 就业政策 -->\r\n\t\t<!-- 版本1 -->\r\n\t\t<view class=\"service-title-2\" style=\"margin-left: 22upx\"></view>\r\n\t\t<view class=\"hot-activities\">\r\n\t\t\t<view class=\"tabs\">\r\n\t\t\t\t<text @click=\"handerTab(0)\" :class=\"['tab', activeTab == 0? 'active': '']\">就业政策</text>\r\n\t\t\t\t<text @click=\"handerTab(1)\" :class=\"['tab', activeTab == 1? 'active': '']\">就业雷达</text>\r\n\t\t\t</view>\r\n\t\t\t<uni-list style=\"margin-top: 30upx;\" v-if=\"activities.length\">\r\n\t\t\t\t<cellVue v-for=\"(item, index) in activities\" :obj=\"item\" :key=\"index\" @click=\"goDetail(item)\" />\r\n\t\t\t\t<view class=\"more-box-1\" @click=\"goJumpMore\">更多 <uni-icons type=\"right\" color=\"#CCCCCC\"\r\n\t\t\t\t\t\tsize=\"16\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t</uni-list>\r\n\t\t\t<view class=\"empty-box\" v-else>\r\n\t\t\t\t暂无数据\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- 版本2 -->\r\n\t\t<!-- \t\t<view style=\"display: flex;align-items: flex-end;margin-left: 32upx;\">\r\n\t\t\t<view class=\"jy-tit\"></view>\r\n\t\t\t<view class=\"more-box\" @click=\"goJumpMore(0)\">更多 <uni-icons type=\"right\" color=\"#CCCCCC\"\r\n\t\t\t\t\tsize=\"16\"></uni-icons>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"hot-activities\">\r\n\t\t\t<uni-list style=\"margin-top: 30upx;\" v-if=\"activities.length\">\r\n\t\t\t\t<cellVue v-for=\"(item, index) in activities\" :obj=\"item\" :key=\"index\" @click=\"goDetail(item,0)\" />\r\n\t\t\t</uni-list>\r\n\t\t\t<view class=\"empty-box\" v-else>\r\n\t\t\t\t暂无数据\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"display: flex;align-items: flex-end;margin-left: 32upx;\">\r\n\t\t\t<view class=\"jy-tit-2\"></view>\r\n\t\t\t<view class=\"more-box\" @click=\"goJumpMore(1)\">更多 <uni-icons type=\"right\" color=\"#CCCCCC\"\r\n\t\t\t\t\tsize=\"16\"></uni-icons>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"hot-activities\">\r\n\t\t\t<uni-list style=\"margin-top: 30upx;\" v-if=\"jyLeiDa.length\">\r\n\t\t\t\t<cellVue v-for=\"(item, index) in jyLeiDa\" :obj=\"item\" :key=\"index\" @click=\"goDetail(item,1)\" />\r\n\t\t\t</uni-list>\r\n\t\t\t<view class=\"empty-box\" v-else>\r\n\t\t\t\t暂无数据\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<!-- footer -->\r\n\t\t<view class=\"footer-box\">\r\n\t\t\t<text>主办单位名称：泉州市丰泽区人力资源和社会保障局</text>\r\n\t\t\t<text>投诉建议渠道：电话0595-22508200</text>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetPolicyLists,\r\n\t\tgetCompanyLists\r\n\t} from \"../../api/rs\"\r\n\timport cellVue from \"../../components/cell.vue\";\r\n\timport file from '../../mixins/file';\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tserviceLinks: [{\r\n\t\t\t\t\t\ttitle: '福建人社',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-0.png\"),\r\n\t\t\t\t\t\twebIdx: 0,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '福建人才融媒',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-2.png\"),\r\n\t\t\t\t\t\twebIdx: 1,\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '福建省流动人员档案公共服务平台',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-1.png\"),\r\n\t\t\t\t\t\twebIdx: 5\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '福建省职业培训小助手',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-6.png\"),\r\n\t\t\t\t\t\twebIdx: 7\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '泉州人才港湾',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-3.png\"),\r\n\t\t\t\t\t\twebIdx: 2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// {\r\n\t\t\t\t\t// \ttitle: '大泉州人才网',\r\n\t\t\t\t\t// \ticon: require(\"../../static/icons/service-4.png\"),\r\n\t\t\t\t\t// \twebIdx: 3\r\n\t\t\t\t\t// },\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '海峡AI职途',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-4.png\"),\r\n\t\t\t\t\t\twebIdx: 9\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '泉就业公共服务平台',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-5.png\"),\r\n\t\t\t\t\t\twebIdx: 4\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '泉州就业和人才人事公共服务中心',\r\n\t\t\t\t\t\ticon: require(\"../../static/icons/service-6.png\"),\r\n\t\t\t\t\t\twebIdx: 6\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tpolicyLists: [],\r\n\t\t\t\tactivities: [{\r\n\t\t\t\t\t\ttitle: '丰泽区\"稳岗扩工\"十五条措施10条内容',\r\n\t\t\t\t\t\tdate: '2023-06-25'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '青年人才集聚行动州州直部门开八条政策措施',\r\n\t\t\t\t\t\tdate: '2023-06-23'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '丰泽区高校毕业生就业创业政策',\r\n\t\t\t\t\t\tdate: '2023-07-10'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttitle: '丰泽区\"稳岗扩工\"十五条措施',\r\n\t\t\t\t\t\tdate: '2023-06-25'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tjyLeiDa: [],\r\n\t\t\t\tnoticeBarLists: [],\r\n\t\t\t\tactiveTab: 0,\r\n\t\t\t\tnoticeList: [{\r\n\t\t\t\t\t\tid: 1,\r\n\t\t\t\t\t\ttitle: '征程这些伟大精神 串连起中国共产党人的精神谱系'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 2,\r\n\t\t\t\t\t\ttitle: '增强水运发展新动能 前5月港口货物吞吐量增长7.9%'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 3,\r\n\t\t\t\t\t\ttitle: '多地持续高温 各地采取措施积极应对'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 4,\r\n\t\t\t\t\t\ttitle: '中非经贸博览会见证中非合作深度'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tid: 5,\r\n\t\t\t\t\t\ttitle: '国安家安得民心 保驾护航促治兴'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t};\r\n\t\t},\r\n\t\tcomponents: {\r\n\t\t\tcellVue\r\n\t\t},\r\n\t\tmixins: [file],\r\n\t\tmethods: {\r\n\t\t\thanderTab(tab) {\r\n\t\t\t\tthis.activeTab = tab\r\n\t\t\t\tthis.fetchInit(tab)\r\n\t\t\t},\r\n\t\t\tjumpService(item, idx) {\r\n\t\t\t\tconsole.log(\"item\", item)\r\n\t\t\t\tconst url = `https://ai.enzenith.com/job/#/codePage`\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/bussinessPages/webview/webview?url=${url}&idx=${item.webIdx}&tit=${item.title}&useCode=1`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 获取政策列表和岗位列表\r\n\t\t\tasync fetchInit(type) {\r\n\t\t\t\tswitch (type) {\r\n\t\t\t\t\tcase 0: {\r\n\t\t\t\t\t\tconst res = await getPolicyLists({\r\n\t\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\t\tpageSize: 5\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tconsole.log(\"政策列表\", res)\r\n\t\t\t\t\t\tthis.activities = res?.records.map(el => {\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t...el,\r\n\t\t\t\t\t\t\t\ttitle: el.fileName.length > 21 ? el.fileName.substr(0, 21) + '...' : el\r\n\t\t\t\t\t\t\t\t\t.fileName,\r\n\t\t\t\t\t\t\t\tdate: el.createTime\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.policyLists = JSON.parse(JSON.stringify(this.activities))\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1: {\r\n\t\t\t\t\t\tconst res = await getCompanyLists({\r\n\t\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\t\tpageSize: 5,\r\n\t\t\t\t\t\t\tfirmStatus: 0,\r\n\t\t\t\t\t\t\tfirmServiceObj: ''\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tconsole.log(\"公司列表\", res)\r\n\t\t\t\t\t\t// jyLeiDa\r\n\t\t\t\t\t\tthis.activities = res?.records.map(el => {\r\n\t\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\t\t...el,\r\n\t\t\t\t\t\t\t\ttitle: el.firmName.length > 23 ? el.firmName.substr(0, 23) + '...' : el\r\n\t\t\t\t\t\t\t\t\t.firmName,\r\n\t\t\t\t\t\t\t\tdate: el.createTime\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault: {}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandlerAction(type) {\r\n\t\t\t\t// https://ai.enzenith.com\r\n\t\t\t\t// https://aiv2.enzenith.com\r\n\t\t\t\tconst urls = {\r\n\t\t\t\t\tjydt: 'https://ai.enzenith.com/job/#/rsjmap',\r\n\t\t\t\t\twyqz: 'https://ai.enzenith.com/job/#/rsjPolicyLeidaPage',\r\n\t\t\t\t\tjyzc: 'https://ai.enzenith.com/job/#/rsjPolicyPage'\r\n\t\t\t\t}\r\n\t\t\t\t// 版本1\r\n\t\t\t\t// const url = urls[type]\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t// \turl: `/bussinessPages/webview/webview?url=${url}`,\r\n\t\t\t\t// })\r\n\t\t\t\t//版本2\r\n\t\t\t\tconst urlPages = {\r\n\t\t\t\t\tjydt: '/bussinessPages/mapPage/mapPage',\r\n\t\t\t\t\tjyzc: '/bussinessPages/policyPage/policyPage',\r\n\t\t\t\t\twyqz: '/bussinessPages/leidaPage/leidaPage',\r\n\t\t\t\t\t// https://aiv2.enzenith.com/job/#/rsjPolicyPage\r\n\t\t\t\t}\r\n\t\t\t\tswitch (type) {\r\n\t\t\t\t\tcase 'jydt':\r\n\t\t\t\t\tcase 'wyqz':\r\n\t\t\t\t\tcase 'jyzc': {\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: urlPages[type]\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\t// case 'wyqz': {\r\n\t\t\t\t\t// \tconst url = urls[type]\r\n\t\t\t\t\t// \tuni.navigateTo({\r\n\t\t\t\t\t// \t\turl: `/bussinessPages/webview/webview?url=${url}`,\r\n\t\t\t\t\t// \t})\r\n\t\t\t\t\t// }\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tjumpAi() {\r\n\t\t\t\tlet path = 'https://ai.enzenith.com/job/#/home'\r\n\t\t\t\t// let path = 'https://aiv2.enzenith.com/job/#/home'\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/bussinessPages/webview/webview?url=${path}&code=Key_rsj`,\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgoJumpMore(val) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tactiveTab\r\n\t\t\t\t} = this\r\n\t\t\t\tswitch (activeTab) {\r\n\t\t\t\t\tcase 0: {\r\n\t\t\t\t\t\tthis.handlerAction('jyzc')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1: {\r\n\t\t\t\t\t\tthis.handlerAction('wyqz')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoDetailPolicy(item) {\r\n\t\t\t\tthis.handlerOpenPriviewFile(item)\r\n\t\t\t},\r\n\t\t\tgoDetail(item, val) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tactiveTab\r\n\t\t\t\t} = this\r\n\t\t\t\tswitch (Number(activeTab)) {\r\n\t\t\t\t\tcase 0: {\r\n\t\t\t\t\t\tthis.handlerOpenPriviewFile(item)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 1: {\r\n\t\t\t\t\t\tconst url = `https://ai.enzenith.com/job/#/rsjPolicyLeidaDetail`\r\n\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\turl: `/bussinessPages/webview/webview?url=${url}&id=${item.id}`,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault: {}\r\n\t\t\t\t\tbreak\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.fetchInit(0)\r\n\t\t\tthis.fetchInit(1)\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t@import url(\"./index.scss\");\r\n\r\n\t.empty-box {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 30upx;\r\n\t\tcolor: #8D94A2;\r\n\t\tfont-size: 28upx;\r\n\t}\r\n\r\n\t.more-box-1 {\r\n\t\tcolor: #CED2DE;\r\n\t\tfont-size: 26upx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding-bottom: 12upx;\r\n\t}\r\n\r\n\t.footer-box {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-sizing: border-box;\r\n\t\t// padding: 20upx 88upx;\r\n\t\tcolor: #8D94A2;\r\n\t\tfont-size: 24upx;\r\n\t\ttext-align: center;\r\n\t\t// font-size: 28upx;\r\n\t}\r\n\t::v-deep .uni-swiper-dot {\r\n\t    display: inline-block;\r\n\t    width: 22upx;\r\n\t    height: 5px;\r\n\t    cursor: pointer;\r\n\t    -webkit-transition-property: background-color;\r\n\t    transition-property: background-color;\r\n\t    -webkit-transition-timing-function: ease;\r\n\t    transition-timing-function: ease;\r\n\t    background: rgba(0,0,0,.3);\r\n\t    border-radius: 30upx;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\""], "sourceRoot": ""}