<template>
	<view @click="$emit('onDetail', obj)" class="company-cell">
		<view class="header-cell">
			<view class="company-title over-text">{{ obj.firmName }}</view>
			<view class="detail-link">详情 <uni-icons type="right" color="#999999" size="16"></uni-icons></view>
		</view>
		<view class="company-info">
			<view class="info-item">
				<uni-icons type="person-filled" color="#f56c6c" size="16"></uni-icons>
				<text class="over-text">{{ obj.postNameToString || '-' }}</text>
			</view>
			<view class="info-item">
				<uni-icons type="phone-filled" color="#4a89dc" size="16"></uni-icons>
				<text class="over-text">{{ obj.contactTel || '-' }}</text>
			</view>
			<view class="info-item">
				<uni-icons type="location-filled" color="#4a89dc" size="16"></uni-icons>
				<text class="over-text">{{ obj.firmAddress || '-' }}</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: "PolicyCell",
		props: {
			obj: {
				type: Object,
				default: () => {
					return {
						idx: 0,
						fileName: '',
						issuerStr: ''
					}
				}
			}
		},
		data() {
			return {

			}
		}
	}
</script>

<style lang="scss" scoped>
	.company-cell {
		// margin-bottom: 10px;
		width: 95%;
		border-radius: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.07);
		padding: 24upx;
		box-sizing: border-box;
		margin: 20upx auto;
		background: #FFFFFF;
		height: auto;
	}

	::v-deep .uni-list-item__container[data-v-296a3d7e] {
		flex-direction: column !important;
	}

	.header-cell {
		width: 100%;
		display: flex;
		justify-content: space-between;
	}

	.company-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 5px;
		width: 85%;
	}

	.company-info {
		display: flex;
		flex-direction: column;
		gap: 5px;
	}

	.info-item {
		display: flex;
		align-items: center;
		font-size: 14px;
		color: #666;
	}

	.info-item .uni-icons {
		margin-right: 5px;
	}

	.detail-link {
		color: #999;
		font-size: 14px;
		display: flex;
		align-items: center;
	}

	.pulldown {
		height: calc(100vh - 120px);
	}

	.over-text {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
</style>