(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uni-data-select/components/uni-data-select/uni-data-select"],{330:function(e,t,n){"use strict";n.r(t);var a=n(331),i=n(333);for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n(335);var c,r=n(32),u=Object(r["default"])(i["default"],a["render"],a["staticRenderFns"],!1,null,null,null,!1,a["components"],c);u.options.__file="uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue",t["default"]=u.exports},331:function(e,t,n){"use strict";n.r(t);var a=n(332);n.d(t,"render",(function(){return a["render"]})),n.d(t,"staticRenderFns",(function(){return a["staticRenderFns"]})),n.d(t,"recyclableRender",(function(){return a["recyclableRender"]})),n.d(t,"components",(function(){return a["components"]}))},332:function(e,t,n){"use strict";var a;n.r(t),n.d(t,"render",(function(){return i})),n.d(t,"staticRenderFns",(function(){return c})),n.d(t,"recyclableRender",(function(){return o})),n.d(t,"components",(function(){return a}));try{a={uniIcons:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(n.bind(null,182))}}}catch(r){if(-1===r.message.indexOf("Cannot find module")||-1===r.message.indexOf(".vue"))throw r;console.error(r.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var i=function(){var e=this,t=e.$createElement,n=(e._self._c,e.showSelector?e.mixinDatacomResData.length:null),a=e.showSelector&&0!==n?e.__map(e.mixinDatacomResData,(function(t,n){var a=e.__get_orig(t),i=e.formatItemName(t);return{$orig:a,m0:i}})):null;e.$mp.data=Object.assign({},{$root:{g0:n,l0:a}})},o=!1,c=[];i._withStripped=!0},333:function(e,t,n){"use strict";n.r(t);var a=n(334),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},334:function(e,t,n){"use strict";(function(e,n){Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={name:"uni-data-select",mixins:[e.mixinDatacom||{}],props:{localdata:{type:Array,default:function(){return[]}},value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},label:{type:String,default:""},placeholder:{type:String,default:"请选择"},emptyTips:{type:String,default:"无选项"},clear:{type:Boolean,default:!0},defItem:{type:Number,default:0},disabled:{type:Boolean,default:!1},format:{type:String,default:""},placement:{type:String,default:"bottom"}},data:function(){return{showSelector:!1,current:"",mixinDatacomResData:[],apps:[],channels:[],cacheKey:"uni-data-select-lastSelectedValue"}},created:function(){var e=this;this.debounceGet=this.debounce((function(){e.query()}),300),this.collection&&!this.localdata.length&&this.debounceGet()},computed:{typePlaceholder:function(){var e={"opendb-stat-app-versions":"版本","opendb-app-channels":"渠道","opendb-app-list":"应用"},t=this.placeholder,n=e[this.collection];return n?t+n:t},valueCom:function(){return this.value},textShow:function(){var e=this.current;return e},getOffsetByPlacement:function(){switch(this.placement){case"top":return"bottom:calc(100% + 12px);";case"bottom":return"top:calc(100% + 12px);"}}},watch:{localdata:{immediate:!0,handler:function(e,t){Array.isArray(e)&&t!==e&&(this.mixinDatacomResData=e)}},valueCom:function(e,t){this.initDefVal()},mixinDatacomResData:{immediate:!0,handler:function(e){e.length&&this.initDefVal()}}},methods:{debounce:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,n=null;return function(){for(var a=this,i=arguments.length,o=new Array(i),c=0;c<i;c++)o[c]=arguments[c];n&&clearTimeout(n),n=setTimeout((function(){e.apply(a,o)}),t)}},query:function(){this.mixinDatacomEasyGet()},onMixinDatacomPropsChange:function(){this.collection&&this.debounceGet()},initDefVal:function(){var e="";if(!this.valueCom&&0!==this.valueCom||this.isDisabled(this.valueCom)){var t;if(this.collection&&(t=this.getCache()),t||0===t)e=t;else{var n="";this.defItem>0&&this.defItem<=this.mixinDatacomResData.length&&(n=this.mixinDatacomResData[this.defItem-1].value),e=n}(e||0===e)&&this.emit(e)}else e=this.valueCom;var a=this.mixinDatacomResData.find((function(t){return t.value===e}));this.current=a?this.formatItemName(a):""},isDisabled:function(e){var t=!1;return this.mixinDatacomResData.forEach((function(n){n.value===e&&(t=n.disable)})),t},clearVal:function(){this.emit(""),this.collection&&this.removeCache()},change:function(e){e.disable||(this.showSelector=!1,this.current=this.formatItemName(e),this.emit(e.value))},emit:function(e){this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.collection&&this.setCache(e)},toggleSelector:function(){this.disabled||(this.showSelector=!this.showSelector)},formatItemName:function(e){var t=e.text,n=e.value,a=e.channel_code;if(a=a?"(".concat(a,")"):"",this.format){var i="";for(var o in i=this.format,e)i=i.replace(new RegExp("{".concat(o,"}"),"g"),e[o]);return i}return this.collection.indexOf("app-list")>0?"".concat(t,"(").concat(n,")"):t||"未命名".concat(a)},getLoadData:function(){return this.mixinDatacomResData},getCurrentCacheKey:function(){return this.collection},getCache:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getCurrentCacheKey(),t=n.getStorageSync(this.cacheKey)||{};return t[e]},setCache:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getCurrentCacheKey(),a=n.getStorageSync(this.cacheKey)||{};a[t]=e,n.setStorageSync(this.cacheKey,a)},removeCache:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getCurrentCacheKey(),t=n.getStorageSync(this.cacheKey)||{};delete t[e],n.setStorageSync(this.cacheKey,t)}}};t.default=a}).call(this,n(287)["uniCloud"],n(2)["default"])},335:function(e,t,n){"use strict";n.r(t);var a=n(336),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},336:function(e,t,n){}}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uni-data-select/components/uni-data-select/uni-data-select-create-component',
    {
        'uni_modules/uni-data-select/components/uni-data-select/uni-data-select-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(330))
        })
    },
    [['uni_modules/uni-data-select/components/uni-data-select/uni-data-select-create-component']]
]);
