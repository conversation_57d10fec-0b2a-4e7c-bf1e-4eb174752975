<view data-event-opts="{{[['tap',[['emptyViewClick',['$event']]]]]}}" class="{{['data-v-0275a1ae',(true)?'zp-container':'',(emptyViewFixed)?'zp-container-fixed':'']}}" style="{{$root.s0}}" bindtap="__e"><view class="zp-main data-v-0275a1ae"><block wx:if="{{!$root.g0}}"><image class="{{['data-v-0275a1ae',(unit==='rpx')?'zp-main-image-rpx':'',(unit==='px')?'zp-main-image-px':'']}}" style="{{$root.s1}}" src="{{emptyImg}}"></image></block><block wx:else><image class="{{['data-v-0275a1ae',(unit==='rpx')?'zp-main-image-rpx':'',(unit==='px')?'zp-main-image-px':'']}}" style="{{$root.s2}}" mode="aspectFit" src="{{emptyViewImg}}"></image></block><text class="{{['zp-main-title','data-v-0275a1ae',(unit==='rpx')?'zp-main-title-rpx':'',(unit==='px')?'zp-main-title-px':'']}}" style="{{$root.s3}}">{{emptyViewText}}</text><block wx:if="{{showEmptyViewReload}}"><text data-event-opts="{{[['tap',[['reloadClick',['$event']]]]]}}" class="{{['data-v-0275a1ae',(true)?'zp-main-error-btn':'',(unit==='rpx')?'zp-main-error-btn-rpx':'',(unit==='px')?'zp-main-error-btn-px':'']}}" style="{{$root.s4}}" catchtap="__e">{{emptyViewReloadText}}</text></block></view></view>