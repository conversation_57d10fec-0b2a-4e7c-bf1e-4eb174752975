<view data-event-opts="{{[['tap',[['$emit',['onDetail','$0'],['obj']]]]]}}" class="company-cell data-v-33311db3" bindtap="__e"><view class="header-cell data-v-33311db3"><view class="company-title over-text data-v-33311db3">{{obj.firmManagementName}}</view><view class="detail-link data-v-33311db3">详情<uni-icons vue-id="32f319b3-1" type="right" color="#999999" size="16" class="data-v-33311db3" bind:__l="__l"></uni-icons></view></view><view class="company-info data-v-33311db3"><view class="info-item data-v-33311db3"><text class="over-text float-tit data-v-33311db3">所属街道：</text><text class="over-text float-value data-v-33311db3">{{obj.firmStreet||'-'}}</text></view><view class="info-item data-v-33311db3"><text class="over-text float-tit data-v-33311db3">求职意向：</text><text class="over-text float-value data-v-33311db3">{{obj.firmManagementPostName||'-'}}</text></view><view class="info-item data-v-33311db3"><text class="over-text float-tit data-v-33311db3">提交时间：</text><text class="over-text float-value data-v-33311db3">{{obj.createTime||'-'}}</text></view></view></view>