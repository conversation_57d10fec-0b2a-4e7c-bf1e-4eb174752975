<template>
	<view class="detail-container">
		<!-- 顶部公司信息区域 -->
		<view class="company-header">
			<view class="company-basic-info">
				<view class="company-name">{{cName || '-'}}</view>
				<view class="company-address">
					<uni-icons type="location" color="#fff" size="14" />
					<text>{{cAddress || '-'}}</text>
				</view>
				<view class="company-phone">
					<uni-icons type="phone" color="#fff" size="14" />
					<text>{{cTel || '-'}}</text>
				</view>
			</view>
		</view>
		<view style="height: 990upx; overflow-y: auto;">
			<!-- 职位信息卡片 -->
			<view class="job-card">
				<view class="job-title">{{infos.postName}}：{{infos.recruitingNumber || 0}}名</view>
				<view class="job-info">
					<view class="job-info-item">
						<text class="info-label">岗位类别：</text>
						<text class="info-value">{{infos.postType || '-'}}</text>
					</view>
					<view class="job-info-item">
						<text class="info-label">福利待遇：</text>
						<text class="info-value">{{infos.welfareTreatment || '-'}}</text>
					</view>
					<view class="job-info-item">
						<text class="info-label">工作地点：</text>
						<text class="info-value">{{infos.workPlace || '-'}}</text>
					</view>
					<view class="job-info-item">
						<text class="info-label">工资：</text>
						<text class="info-value">{{infos.salary || '-'}}</text>
					</view>
					<view class="job-info-item">
						<text class="info-label">有效期限：</text>
						<text class="info-value">{{infos.validity ? infos.validity +'天': '长期'}}</text>
					</view>
				</view>
			</view>

			<!-- 岗位要求 -->
			<view class="job-requirements">
				<view class="section-title">岗位要求：</view>
				<view class="requirement-list">
					<view class="requirement-item">
						<rich-text class="item-content" :nodes="infos.postRequire"></rich-text>
					</view>
				</view>
			</view>

			<!-- 工作环境 -->
			<view class="work-environment">
				<view class="section-title">工作环境：</view>
				<view class="environment-list">
					<rich-text class="item-content" :nodes="infos.workEnv"></rich-text>
				</view>
			</view>
		</view>
		<button class="fixed-btn" @click="goTJ">提交意向</button>
	</view>
</template>

<script>
	import {
		getPositionServiceDetail
	} from '@/api/rs.js'
	export default {
		name: 'pgwDetail',
		data() {
			return {
				infos: {},
				cName: '',
				cAddress: '',
				cTel: '',
				userInfo: null,
				useLogin: false,
				firmStreet: ''
			};
		},
		methods: {
			goBack() {
				uni.navigateBack();
			},
			goTJ() {
				if (this.useLogin) {
					uni.navigateTo({
						url: `/bussinessPages/tjForm/tjForm?cName=${this.cName}&companyId=${this.companyId}&gwId=${this.gwId}&firmStreet=${this.firmStreet}`
					})
				} else {
					uni.showModal({
						content: "暂未登录，请先登录后再进行操作",
						success(res) {
							console.log("r3s", res)
							if (res.cancel) {} else {
								uni.switchTab({
									url: '/pages/mine/mine'
								})
							}
						}
					})
				}
			},
		},
		onShow() {
			const userInfo = uni.getStorageSync('userInfo');
			const userToken = uni.getStorageSync('userToken')
			this.useLogin = userToken ? true : false
		},
		onLoad(options) {
			console.log("options", options)
			let id = options.id
			this.cName = options.cName || ''
			this.cAddress = [null, 'null'].includes(options.cAddress) ? '-' : options.cAddress
			this.cTel = [null, 'null'].includes(options.cTel) ? '-' : options.cTel
			this.companyId = options.companyId || ''
			this.gwId = options.id || ''
			this.firmStreet = options.firmStreet || ''
			if (id) {
				getPositionServiceDetail(id).then(res => {
					console.log("resrsereresr", res)
					// gw详情
					this.infos = res
				})
			}
		}
	};
</script>

<style>
	.detail-container {
		background-color: #f5f7fa;
		/* min-height: 100vh; */
		/* padding-bottom: 80px; */
	}

	.fixed-btn {
		position: fixed;
		bottom: 5%;
		left: 5%;
		width: 90%;
		background-color: #4A89DC;
		color: #fff;
		border-radius: 50rpx;
	}

	.company-header {
		background-color: #4a89dc;
		color: #fff;
		padding: 20px 15px 30px;
		border-radius: 0 0 20px 20px;
		position: relative;
	}

	.back-icon {
		position: absolute;
		top: 15px;
		left: 15px;
	}

	.company-basic-info {
		padding-top: 20px;
	}

	.company-name {
		font-size: 18px;
		font-weight: bold;
		margin-bottom: 10px;
	}

	.company-address,
	.company-phone {
		display: flex;
		align-items: center;
		font-size: 14px;
		margin-bottom: 5px;
	}

	.company-address .uni-icons,
	.company-phone .uni-icons {
		margin-right: 5px;
	}

	.job-card,
	.job-requirements,
	.work-environment {
		background-color: #fff;
		border-radius: 10px;
		margin: 15px;
		padding: 15px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
	}

	.job-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
	}

	.job-info-item {
		display: flex;
		margin-bottom: 10px;
		font-size: 14px;
		color: #666;
	}

	.info-label {
		flex-shrink: 0;
		color: #333;
		font-weight: 500;
	}

	.section-title {
		font-size: 16px;
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
	}

	.requirement-item,
	.environment-item {
		display: flex;
		margin-bottom: 8px;
		font-size: 14px;
		color: #666;
	}

	.item-num {
		margin-right: 5px;
		flex-shrink: 0;
	}

	.item-content {
		flex: 1;
	}

	.bottom-button {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		padding: 15px;
		background-color: #fff;
		box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
	}
</style>