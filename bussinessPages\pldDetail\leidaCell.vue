<template>
  <view class="job-card" @click="handleClick">
    <view class="job-title">{{ obj.postName || '-' }}：{{ obj.recruitingNumber || '0' }}名</view>
    <view class="job-desc">
      <view class="job-item">
        <rich-text :nodes="obj.postRequire"></rich-text>
      </view>
    </view>
    <view class="job-salary">
      <text class="salary-amount">{{ obj.salary || '0' }}</text>
      <text class="salary-unit">元/月</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'GwCell',
  props: {
    // 职位名称
    postName: {
      type: String,
      default: ''
    },
    // 招聘人数
    recruitingNumber: {
      type: [Number, String],
      default: 0
    },
    // 职位要求
    postRequire: {
      type: String,
      default: ''
    },
    // 薪资
    salary: {
      type: [Number, String],
      default: ''
    },
    // 职位ID
    id: {
      type: [Number, String],
      default: ''
    },
	obj: {
		type: Object,
		default: () => {}
	}
  },
  methods: {
    handleClick() {
      this.$emit('click', {
        id: this.id,
        postName: this.postName,
        recruitingNumber: this.recruitingNumber,
        postRequire: this.postRequire,
        salary: this.salary,
		...this.obj
      })
    }
  }
}
</script>

<style scoped>
.job-card {
  background-color: #fff;
  border-radius: 10px;
  margin: 15upx 0 0 !important;
  padding: 0 30upx 0 0 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.job-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.job-desc {
  margin-bottom: 10px;
}

.job-item {
  display: flex;
  margin-bottom: 5px;
  font-size: 14px;
  color: #666;
}

.job-salary {
  text-align: right;
}

.salary-amount {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
}

.salary-unit {
  font-size: 14px;
  color: #999;
}
</style>