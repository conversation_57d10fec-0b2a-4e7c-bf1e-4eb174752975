{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/leidaPage/leidaPage.vue?cd0d", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/leidaPage/leidaPage.vue?93b7", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/leidaPage/leidaPage.vue?e27d", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/leidaPage/leidaPage.vue?15cd", "uni-app:///bussinessPages/leidaPage/leidaPage.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/leidaPage/leidaPage.vue?1f9a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniSearchBar", "uniDataSelect", "zPaging", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "l0", "_self", "_c", "__map", "dataList", "cell", "idx", "$orig", "__get_orig", "a0", "Object", "assign", "$mp", "data", "$root", "recyclableRender", "staticRenderFns", "_withStripped", "name", "api", "apiBase", "useQueryPage", "body", "firmServiceObj", "firmStreet", "postTag", "firmName", "firmStatus", "index", "size", "total", "useFunCallApi", "option1", "option2", "option3", "mixins", "leidaCellVue", "onLoad", "methods", "handlerJump", "uni", "url", "onClear", "onSearch", "onChangeAc", "handlerDetail", "initDict", "xl", "jd", "gw", "text", "value"], "mappings": "6JAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,yCACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,aAAc,WACZ,OAAO,uIAITC,cAAe,WACb,OAAO,0IAITC,QAAS,WACP,OAAO,sHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eAETC,GADKJ,EAAIK,MAAMC,GACVN,EAAIO,MAAMP,EAAIQ,UAAU,SAAUC,EAAMC,GAC/C,IAAIC,EAAQX,EAAIY,WAAWH,GACvBI,EAAKC,OAAOC,OAAO,GAAIN,EAAM,CAAEC,IAAKA,IACxC,MAAO,CACLC,MAAOA,EACPE,GAAIA,OAGRb,EAAIgB,IAAIC,KAAOH,OAAOC,OACpB,GACA,CACEG,MAAO,CACLd,GAAIA,MAKRe,GAAmB,EACnBC,EAAkB,GACtBrB,EAAOsB,eAAgB,G,iCC3DvB,yHAAkwB,eAAG,G,qJC8BrwB,QAIA,W,4HAEA,CACAC,iBACAL,gBACA,OACAM,mCACAC,gBACAC,gBACAC,MACAC,kBACAC,cACAC,WACAC,YACAC,aACAC,QACAC,QACAC,SAEAC,0BAAA,gCACAC,WACAC,WACAC,aAGAC,mBACAjD,YACAkD,gBAEAC,kBACA,iBACA,iBAEAC,SACAC,wBACAC,cACAC,8CAGAC,mBACA,sBACA,oBAEAC,oBAGA,kBAEAC,sBACAnD,2BACA,oBAEAoD,0BAMAL,cACAC,8DAGAK,oBAAA,wKAGA,kDAAAC,SAAA,UACA,+CAAAC,SAAA,UACA,qCAAAC,SACAxD,oBAEA,0DACA,OACAyD,iBACAC,wBAEA,GAEA,0DACA,OACAD,iBACAC,wBAEA,GAEA,0DACA,OACAD,iBACAC,wBAEA,GACA,mBACAD,cACAC,WAEA,mBACAD,YACAC,WAEA1D,oBACA,mBACAyD,YACAC,WAEA1D,oBAAA,qDAEAA,+BAAA,yDA3CA,MAgDA,c,6DChJA,yHAAqmC,eAAG,G", "file": "bussinessPages/leidaPage/leidaPage.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './bussinessPages/leidaPage/leidaPage.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./leidaPage.vue?vue&type=template&id=79c0e63f&scoped=true&\"\nvar renderjs\nimport script from \"./leidaPage.vue?vue&type=script&lang=js&\"\nexport * from \"./leidaPage.vue?vue&type=script&lang=js&\"\nimport style0 from \"./leidaPage.vue?vue&type=style&index=0&id=79c0e63f&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"79c0e63f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/leidaPage/leidaPage.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaPage.vue?vue&type=template&id=79c0e63f&scoped=true&\"", "var components\ntry {\n  components = {\n    uniSearchBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar\" */ \"@/uni_modules/uni-search-bar/components/uni-search-bar/uni-search-bar.vue\"\n      )\n    },\n    uniDataSelect: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-data-select/components/uni-data-select/uni-data-select\" */ \"@/uni_modules/uni-data-select/components/uni-data-select/uni-data-select.vue\"\n      )\n    },\n    zPaging: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/z-paging/components/z-paging/z-paging\" */ \"@/uni_modules/z-paging/components/z-paging/z-paging.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.dataList, function (cell, idx) {\n    var $orig = _vm.__get_orig(cell)\n    var a0 = Object.assign({}, cell, { idx: idx })\n    return {\n      $orig: $orig,\n      a0: a0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaPage.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaPage.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"area-container\">\r\n\t\t<!-- 顶部搜索栏 -->\r\n\t\t<view class=\"search-header\">\r\n\t\t\t<view class=\"search-input-box\">\r\n\t\t\t\t<uni-search-bar style=\"width: 90%;\" bgColor=\"#fff\" v-model=\"body.firmName\" placeholder=\"请输入搜索关键词\"\r\n\t\t\t\t\tclearButton=\"auto\" cancelButton=\"none\" @confirm=\"search\" @clear=\"onClear\">\r\n\t\t\t\t</uni-search-bar>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"search-btn\" @click=\"search\">搜索</view>\r\n\t\t</view>\r\n\r\n\t\t<!-- 筛选条件 -->\r\n\t\t<view class=\"filter-tabs\">\r\n\t\t\t<uni-data-select style=\"width: 32%\" :clear=\"false\" v-model=\"body.firmServiceObj\" :localdata=\"option1\"\r\n\t\t\t\t@change=\"onChangeAc\"></uni-data-select>\r\n\t\t\t<uni-data-select style=\"width: 32%\" :clear=\"false\" v-model=\"body.firmStreet\" :localdata=\"option2\"\r\n\t\t\t\t@change=\"onChangeAc\"></uni-data-select>\r\n\t\t\t<uni-data-select style=\"width: 32%\" :clear=\"false\" v-model=\"body.postTag\" :localdata=\"option3\"\r\n\t\t\t\t@change=\"onChangeAc\"></uni-data-select>\r\n\t\t</view>\r\n\r\n\t\t<!-- 公司列表 -->\r\n\t\t<z-paging :auto=\"false\" :fixed=\"false\" height=\"77vh\" ref=\"paging\" @query=\"queryList\" @onRefresh=\"onRefresh\">\r\n\t\t\t<leidaCellVue v-for=\"(cell,idx) in dataList\" :key=\"idx\" :obj=\"{...cell,idx}\" @onDetail=\"handlerDetail\" />\r\n\t\t</z-paging>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetCompanyLists,\r\n\t\tgetDictionary\r\n\t} from '../../api/rs.js'\r\n\timport table from '../../mixins/table';\r\n\timport leidaCellVue from './leidaCell.vue'\r\n\texport default {\r\n\t\tname: 'leidaPage',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tapi: '/api/zero/platform/firm/list',\r\n\t\t\t\tapiBase: '$http',\r\n\t\t\t\tuseQueryPage: false,\r\n\t\t\t\tbody: {\r\n\t\t\t\t\tfirmServiceObj: '',\r\n\t\t\t\t\tfirmStreet: '',\r\n\t\t\t\t\tpostTag: '',\r\n\t\t\t\t\tfirmName: '',\r\n\t\t\t\t\tfirmStatus: 0,\r\n\t\t\t\t\tindex: 1,\r\n\t\t\t\t\tsize: 10,\r\n\t\t\t\t\ttotal: 0\r\n\t\t\t\t},\r\n\t\t\t\tuseFunCallApi: (data) => getCompanyLists(data),\r\n\t\t\t\toption1: [],\r\n\t\t\t\toption2: [],\r\n\t\t\t\toption3: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tmixins: [table],\r\n\t\tcomponents: {\r\n\t\t\tleidaCellVue\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\tthis.fetchList()\r\n\t\t\tthis.initDict()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\thandlerJump(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/rsjPolicyLeidaDetail?id=' + item.id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonClear() {\r\n\t\t\t\tthis.body.firmName = ''\r\n\t\t\t\tthis.onRestFetch()\r\n\t\t\t},\r\n\t\t\tonSearch() {\r\n\t\t\t\t// 实现搜索功能\r\n\t\t\t\t// console.log('搜索:', this.searchValue)\r\n\t\t\t\tthis.onRefresh()\r\n\t\t\t},\r\n\t\t\tonChangeAc() {\r\n\t\t\t\tconsole.log(\"ffsdfsdfsdf\")\r\n\t\t\t\tthis.onRestFetch()\r\n\t\t\t},\r\n\t\t\thandlerDetail(item) {\r\n\t\t\t\t// const url = `https://ai.enzenith.com/job/#/rsjPolicyLeidaDetail`\r\n\t\t\t\t// const url = `https://aiv2.enzenith.com/job/#/rsjPolicyLeidaDetail` //测试\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t// \turl: `/bussinessPages/webview/webview?url=${url}&id=${item.id}`,\r\n\t\t\t\t// })\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: `/bussinessPages/pldDetail/pldDetail?id=${item.id}`\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync initDict() {\r\n\r\n\t\t\t\ttry {\r\n\t\t\t\t\tconst xl = await getDictionary('serviceObject')\r\n\t\t\t\t\tconst jd = await getDictionary('zeroStreet')\r\n\t\t\t\t\tconst gw = await getDictionary('postTag')\r\n\t\t\t\t\tconsole.log('xl', xl)\r\n\r\n\t\t\t\t\tthis.option1 = xl?.map(el => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\ttext: el.entryName,\r\n\t\t\t\t\t\t\tvalue: el.entryCode\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}) || []\r\n\r\n\t\t\t\t\tthis.option2 = jd?.map(el => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\ttext: el.entryName,\r\n\t\t\t\t\t\t\tvalue: el.entryName\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}) || []\r\n\t\t\t\t\t\r\n\t\t\t\t\tthis.option3 = gw?.map(el => {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\ttext: el.entryName,\r\n\t\t\t\t\t\t\tvalue: el.entryCode\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}) || []\r\n\t\t\t\t\tthis.option1.unshift({\r\n\t\t\t\t\t\ttext: '全部服务对象',\r\n\t\t\t\t\t\tvalue: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.option2.unshift({\r\n\t\t\t\t\t\ttext: '全部街道',\r\n\t\t\t\t\t\tvalue: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconsole.log('jd', jd)\r\n\t\t\t\t\tthis.option3.unshift({\r\n\t\t\t\t\t\ttext: '全部岗位',\r\n\t\t\t\t\t\tvalue: ''\r\n\t\t\t\t\t})\r\n\t\t\t\t\tconsole.log('gw', gw)\r\n\t\t\t\t} catch (error) {\r\n\t\t\t\t\tconsole.error('获取字典数据失败', error)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style scoped>\r\n\t.area-container {\r\n\t\tbackground-color: #f5f7fa;\r\n\t\tmin-height: 95vh;\r\n\t}\r\n\r\n\t::v-deep .uni-select {\r\n\t\tborder: none !important;\r\n\t}\r\n\r\n\t.search-btn {\r\n\t\twidth: 88upx;\r\n\t\theight: 48upx;\r\n\t\tbackground: #4989FD;\r\n\t\tborder-radius: 8upx;\r\n\t\tcolor: #fff;\r\n\t\tfont-weight: 600;\r\n\t\tfont-size: 28upx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 42upx;\r\n\t\tmargin-right: 1%;\r\n\t}\r\n\r\n\t.bac-icon {\r\n\t\twidth: 34px;\r\n\t\theight: 34px;\r\n\t}\r\n\r\n\t.search-header {\r\n\t\tposition: sticky;\r\n\t\ttop: 0;\r\n\t\tz-index: 100;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-sizing: border-box;\r\n\t\tpadding: 12px 0 0;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-evenly;\r\n\t}\r\n\r\n\t.search-input-box {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.filter-tabs {\r\n\t\tbackground-color: #fff;\r\n\t\tmargin-bottom: 10px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\tpadding: 10px;\r\n\t}\r\n\r\n\t.company-list {\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.company-cell {\r\n\t\tmargin-bottom: 10px;\r\n\t\tborder-radius: 8px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.company-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 5px;\r\n\t}\r\n\r\n\t.company-info {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\tgap: 5px;\r\n\t}\r\n\r\n\t.info-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.info-item .uni-icons {\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.detail-link {\r\n\t\tcolor: #999;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.pulldown {\r\n\t\theight: calc(100vh - 120px);\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaPage.vue?vue&type=style&index=0&id=79c0e63f&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaPage.vue?vue&type=style&index=0&id=79c0e63f&scoped=true&lang=css&\""], "sourceRoot": ""}