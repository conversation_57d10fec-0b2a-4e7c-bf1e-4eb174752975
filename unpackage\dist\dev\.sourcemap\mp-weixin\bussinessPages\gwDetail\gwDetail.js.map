{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/gwDetail/gwDetail.vue?0138", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/gwDetail/gwDetail.vue?3baf", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/gwDetail/gwDetail.vue?cdf0", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/gwDetail/gwDetail.vue?732e", "uni-app:///bussinessPages/gwDetail/gwDetail.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/gwDetail/gwDetail.vue?631b"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "name", "data", "infos", "cName", "c<PERSON><PERSON><PERSON>", "cTel", "userInfo", "useLogin", "firmStreet", "methods", "goBack", "uni", "goTJ", "url", "content", "success", "onShow", "onLoad"], "mappings": "2JAAA,MAGA,aACA,YAFAA,EAAGC,kCAAoCC,EAGvCC,EAAWC,a,gFCLX,oIACIC,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,uCACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCjCvB,yHAAiwB,eAAG,G,gHCmEpwB,Y,EAGA,CACAC,iBACAC,gBACA,OACAC,SACAC,SACAC,YACAC,QACAC,cACAC,YACAC,gBAGAC,SACAC,kBACAC,kBAEAC,gBACA,cACAD,cACAE,uKAGAF,aACAG,0BACAC,oBACA3B,qBACA,UACAuB,aACAE,8BAQAG,kBACA,iCACA,gCACA,mBAEAC,mBAAA,WACA7B,yBACA,WACA,uBACA,gEACA,oDACA,+BACA,mBACA,iCACA,IACA,mDACAA,8BAEA,eAIA,c,6DCjIA,yHAA4kC,eAAG,G", "file": "bussinessPages/gwDetail/gwDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './bussinessPages/gwDetail/gwDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./gwDetail.vue?vue&type=template&id=62b2f9e5&\"\nvar renderjs\nimport script from \"./gwDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./gwDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./gwDetail.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/gwDetail/gwDetail.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gwDetail.vue?vue&type=template&id=62b2f9e5&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gwDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gwDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"detail-container\">\r\n\t\t<!-- 顶部公司信息区域 -->\r\n\t\t<view class=\"company-header\">\r\n\t\t\t<view class=\"company-basic-info\">\r\n\t\t\t\t<view class=\"company-name\">{{cName || '-'}}</view>\r\n\t\t\t\t<view class=\"company-address\">\r\n\t\t\t\t\t<uni-icons type=\"location\" color=\"#fff\" size=\"14\" />\r\n\t\t\t\t\t<text>{{cAddress || '-'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"company-phone\">\r\n\t\t\t\t\t<uni-icons type=\"phone\" color=\"#fff\" size=\"14\" />\r\n\t\t\t\t\t<text>{{cTel || '-'}}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view style=\"height: 990upx; overflow-y: auto;\">\r\n\t\t\t<!-- 职位信息卡片 -->\r\n\t\t\t<view class=\"job-card\">\r\n\t\t\t\t<view class=\"job-title\">{{infos.postName}}：{{infos.recruitingNumber || 0}}名</view>\r\n\t\t\t\t<view class=\"job-info\">\r\n\t\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">岗位类别：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{infos.postType || '-'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">福利待遇：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{infos.welfareTreatment || '-'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">工作地点：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{infos.workPlace || '-'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">工资：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{infos.salary || '-'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"job-info-item\">\r\n\t\t\t\t\t\t<text class=\"info-label\">有效期限：</text>\r\n\t\t\t\t\t\t<text class=\"info-value\">{{infos.validity ? infos.validity +'天': '长期'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 岗位要求 -->\r\n\t\t\t<view class=\"job-requirements\">\r\n\t\t\t\t<view class=\"section-title\">岗位要求：</view>\r\n\t\t\t\t<view class=\"requirement-list\">\r\n\t\t\t\t\t<view class=\"requirement-item\">\r\n\t\t\t\t\t\t<rich-text class=\"item-content\" :nodes=\"infos.postRequire\"></rich-text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 工作环境 -->\r\n\t\t\t<view class=\"work-environment\">\r\n\t\t\t\t<view class=\"section-title\">工作环境：</view>\r\n\t\t\t\t<view class=\"environment-list\">\r\n\t\t\t\t\t<rich-text class=\"item-content\" :nodes=\"infos.workEnv\"></rich-text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<button class=\"fixed-btn\" @click=\"goTJ\">提交意向</button>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tgetPositionServiceDetail\r\n\t} from '@/api/rs.js'\r\n\texport default {\r\n\t\tname: 'pgwDetail',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tinfos: {},\r\n\t\t\t\tcName: '',\r\n\t\t\t\tcAddress: '',\r\n\t\t\t\tcTel: '',\r\n\t\t\t\tuserInfo: null,\r\n\t\t\t\tuseLogin: false,\r\n\t\t\t\tfirmStreet: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgoBack() {\r\n\t\t\t\tuni.navigateBack();\r\n\t\t\t},\r\n\t\t\tgoTJ() {\r\n\t\t\t\tif (this.useLogin) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: `/bussinessPages/tjForm/tjForm?cName=${this.cName}&companyId=${this.companyId}&gwId=${this.gwId}&firmStreet=${this.firmStreet}`\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showModal({\r\n\t\t\t\t\t\tcontent: \"暂未登录，请先登录后再进行操作\",\r\n\t\t\t\t\t\tsuccess(res) {\r\n\t\t\t\t\t\t\tconsole.log(\"r3s\", res)\r\n\t\t\t\t\t\t\tif (res.cancel) {} else {\r\n\t\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/mine/mine'\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tconst userInfo = uni.getStorageSync('userInfo');\r\n\t\t\tconst userToken = uni.getStorageSync('userToken')\r\n\t\t\tthis.useLogin = userToken ? true : false\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tconsole.log(\"options\", options)\r\n\t\t\tlet id = options.id\r\n\t\t\tthis.cName = options.cName || ''\r\n\t\t\tthis.cAddress = [null, 'null'].includes(options.cAddress) ? '-' : options.cAddress\r\n\t\t\tthis.cTel = [null, 'null'].includes(options.cTel) ? '-' : options.cTel\r\n\t\t\tthis.companyId = options.companyId || ''\r\n\t\t\tthis.gwId = options.id || ''\r\n\t\t\tthis.firmStreet = options.firmStreet || ''\r\n\t\t\tif (id) {\r\n\t\t\t\tgetPositionServiceDetail(id).then(res => {\r\n\t\t\t\t\tconsole.log(\"resrsereresr\", res)\r\n\t\t\t\t\t// gw详情\r\n\t\t\t\t\tthis.infos = res\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\t.detail-container {\r\n\t\tbackground-color: #f5f7fa;\r\n\t\t/* min-height: 100vh; */\r\n\t\t/* padding-bottom: 80px; */\r\n\t}\r\n\r\n\t.fixed-btn {\r\n\t\tposition: fixed;\r\n\t\tbottom: 5%;\r\n\t\tleft: 5%;\r\n\t\twidth: 90%;\r\n\t\tbackground-color: #4A89DC;\r\n\t\tcolor: #fff;\r\n\t\tborder-radius: 50rpx;\r\n\t}\r\n\r\n\t.company-header {\r\n\t\tbackground-color: #4a89dc;\r\n\t\tcolor: #fff;\r\n\t\tpadding: 20px 15px 30px;\r\n\t\tborder-radius: 0 0 20px 20px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.back-icon {\r\n\t\tposition: absolute;\r\n\t\ttop: 15px;\r\n\t\tleft: 15px;\r\n\t}\r\n\r\n\t.company-basic-info {\r\n\t\tpadding-top: 20px;\r\n\t}\r\n\r\n\t.company-name {\r\n\t\tfont-size: 18px;\r\n\t\tfont-weight: bold;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.company-address,\r\n\t.company-phone {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 14px;\r\n\t\tmargin-bottom: 5px;\r\n\t}\r\n\r\n\t.company-address .uni-icons,\r\n\t.company-phone .uni-icons {\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.job-card,\r\n\t.job-requirements,\r\n\t.work-environment {\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10px;\r\n\t\tmargin: 15px;\r\n\t\tpadding: 15px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\r\n\t}\r\n\r\n\t.job-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 15px;\r\n\t}\r\n\r\n\t.job-info-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 10px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.info-label {\r\n\t\tflex-shrink: 0;\r\n\t\tcolor: #333;\r\n\t\tfont-weight: 500;\r\n\t}\r\n\r\n\t.section-title {\r\n\t\tfont-size: 16px;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t.requirement-item,\r\n\t.environment-item {\r\n\t\tdisplay: flex;\r\n\t\tmargin-bottom: 8px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.item-num {\r\n\t\tmargin-right: 5px;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.item-content {\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.bottom-button {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tpadding: 15px;\r\n\t\tbackground-color: #fff;\r\n\t\tbox-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gwDetail.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./gwDetail.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}