(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/mine"],{"2ead":function(e,n,t){"use strict";var o=t("9052"),r=t.n(o);r.a},"414b":function(e,n,t){"use strict";t.r(n);var o=t("5fa8"),r=t("4ddf");for(var a in r)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(a);t("2ead");var u=t("828b"),i=Object(u["a"])(r["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=i.exports},"48bc":function(e,n,t){"use strict";(function(e,o){var r=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var a=r(t("7eb4")),u=r(t("ee10")),i=t("3ec1"),c={data:function(){return{hasLogin:!1,userInfo:{},wxQuery:{code:"",telCode:"",avatarUrl:"",nickName:""},count:0,outPutShow:!1}},onLoad:function(){},onShow:function(){this.checkLoginStatus(),console.log("1111111")},methods:{handlerOutPut:function(){this.count+=1,this.outPutShow=this.count>16&&!0},clearStroage:function(){e.clearStorage(),e.reLaunch({url:"/pages/index/index"})},handlerRecord:function(n){switch(n){case 1:this.hasLogin?e.navigateTo({url:"/bussinessPages/tjRecordPages/tjRecordPages"}):e.showToast({title:"请先登录再进行操作",icon:"none"});break;default:e.showToast({title:"升级中",icon:"none"})}},checkLoginStatus:function(){try{var n=e.getStorageSync("userInfo"),t=e.getStorageSync("userToken");t?(this.userInfo=JSON.parse(n),this.hasLogin=!0):(this.userInfo={},this.hasLogin=!1)}catch(o){console.error("获取登录状态失败",o)}},wxLogin:function(){var n=this;e.login({provider:"weixin",success:function(e){console.log("微信登录成功1",e),n.wxQuery.code=e.code,n.getUserInfo(e.code)},fail:function(n){console.error("微信登录失败",n),e.hideLoading(),e.showToast({title:"登录失败，请重试",icon:"none"})}})},wxLoginGetCode:function(){o.login({success:function(){var e=(0,u.default)(a.default.mark((function e(n){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n.code?console.log("wxCode1",n.code):console.log("登录失败！"+n.errMsg);case 1:case"end":return e.stop()}}),e)})));return function(n){return e.apply(this,arguments)}}()})},handlerWxAvatar:function(e){console.log("微信头像",e)},getUserInfo:function(n){var t=this;e.getUserInfo({provider:"weixin",success:function(){var o=(0,u.default)(a.default.mark((function o(r){return a.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:console.log("获取用户信息成功",r),{nickName:r.userInfo.nickName,avatarUrl:r.userInfo.avatarUrl,gender:r.userInfo.gender,code:n},t.wxQuery.avatarUrl=r.userInfo.avatarUrl,t.wxQuery.nickName=r.userInfo.nickName;try{(0,i.wxLoginByCode)(t.wxQuery).then(function(){var n=(0,u.default)(a.default.mark((function n(o){var r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log("登录获取信息",o),t.hasLogin=!0,e.setStorageSync("userToken",o),e.hideLoading(),n.next=6,(0,i.getUserInfo)();case 6:r=n.sent,e.setStorageSync("userInfo",JSON.stringify(r)),t.userInfo=r;case 9:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}())}catch(c){console.error("保存用户信息失败",c),e.hideLoading()}case 5:case"end":return o.stop()}}),o)})));return function(e){return o.apply(this,arguments)}}(),fail:function(n){console.error("获取用户信息失败",n),e.hideLoading(),n.errMsg.indexOf("auth deny")>=0||n.errMsg.indexOf("auth denied")>=0?e.showModal({title:"提示",content:"需要您授权才能继续使用，是否重新授权？",success:function(n){n.confirm&&e.openSetting({success:function(e){console.log("设置页面成功打开",e)}})}}):e.showToast({title:"获取用户信息失败，请重试",icon:"none"})}})},onGetPhoneNumber:function(e){var n=this;return(0,u.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:console.log("获取手机号-111",e),"getPhoneNumber:fail user deny"==e.detail.errMsg?console.log("点击了拒绝！！！"):(n.wxQuery.telCode=e.detail.code,n.sendCodeLogin(n.wxQuery));case 2:case"end":return t.stop()}}),t)})))()},sendCodeLogin:function(e){var n=this;return(0,u.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n.wxLogin();case 1:case"end":return e.stop()}}),e)})))()},logout:function(){var n=this;e.showModal({title:"提示",content:"确定要退出登录吗？",success:function(t){t.confirm&&(e.removeStorageSync("userInfo"),e.removeStorageSync("userToken"),n.userInfo={},n.hasLogin=!1,e.showToast({title:"已退出登录",icon:"success"}))}})},jumpPage:function(n){switch(n){case 1:e.navigateTo({url:"/bussinessPages/updateMine/updateMine"});break;case 2:e.navigateTo({url:"/bussinessPages/setting/setting"});break;default:break}}}};n.default=c}).call(this,t("df3c")["default"],t("3223")["default"])},"4ddf":function(e,n,t){"use strict";t.r(n);var o=t("48bc"),r=t.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(a);n["default"]=r.a},"5fa8":function(e,n,t){"use strict";t.d(n,"b",(function(){return r})),t.d(n,"c",(function(){return a})),t.d(n,"a",(function(){return o}));var o={uniIcons:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(t.bind(null,"2642"))}},r=function(){var e=this.$createElement;this._self._c},a=[]},"6ae6":function(e,n,t){"use strict";(function(e,n){var o=t("47a9");t("bfec");o(t("3240"));var r=o(t("414b"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(r.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},9052:function(e,n,t){}},[["6ae6","common/runtime","common/vendor"]]]);