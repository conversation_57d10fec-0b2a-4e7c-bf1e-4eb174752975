<view class="uni-stat__select"><block wx:if="{{label}}"><label class="uni-label-text hide-on-phone _span">{{label+'：'}}</label></block><view class="{{['uni-stat-box',(current)?'uni-stat__actived':'']}}"><view class="{{['uni-select',(disabled)?'uni-select--disabled':'']}}"><view data-event-opts="{{[['tap',[['toggleSelector',['$event']]]]]}}" class="uni-select__input-box" bindtap="__e"><block wx:if="{{current}}"><view class="uni-select__input-text">{{textShow}}</view></block><block wx:else><view class="uni-select__input-text uni-select__input-placeholder">{{typePlaceholder}}</view></block><block wx:if="{{current&&clear&&!disabled}}"><view data-event-opts="{{[['tap',[['clearVal',['$event']]]]]}}" catchtap="__e"><uni-icons vue-id="011c9238-1" type="clear" color="#c0c4cc" size="24" bind:__l="__l"></uni-icons></view></block><block wx:else><view><uni-icons vue-id="011c9238-2" type="{{showSelector?'top':'bottom'}}" size="14" color="#999" bind:__l="__l"></uni-icons></view></block></view><block wx:if="{{showSelector}}"><view data-event-opts="{{[['tap',[['toggleSelector',['$event']]]]]}}" class="uni-select--mask" bindtap="__e"></view></block><block wx:if="{{showSelector}}"><view class="uni-select__selector" style="{{(getOffsetByPlacement)}}"><view class="{{[placement=='bottom'?'uni-popper__arrow_bottom':'uni-popper__arrow_top']}}"></view><scroll-view class="uni-select__selector-scroll" scroll-y="true"><block wx:if="{{$root.g0===0}}"><view class="uni-select__selector-empty"><text>{{emptyTips}}</text></view></block><block wx:else><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['change',['$0'],[[['mixinDatacomResData','',index]]]]]]]}}" class="uni-select__selector-item" bindtap="__e"><text class="{{[(item.$orig.disable)?'uni-select__selector__disabled':'']}}">{{item.m0}}</text></view></block></block></scroll-view></view></block></view></view></view>