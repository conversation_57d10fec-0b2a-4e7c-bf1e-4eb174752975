(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/mine/mine"],{58:function(e,n,t){"use strict";(function(e,n){var o=t(4);t(26);o(t(25));var r=o(t(59));e.__webpack_require_UNI_MP_PLUGIN__=t,n(r.default)}).call(this,t(1)["default"],t(2)["createPage"])},59:function(e,n,t){"use strict";t.r(n);var o=t(60),r=t(62);for(var s in r)["default"].indexOf(s)<0&&function(e){t.d(n,e,(function(){return r[e]}))}(s);t(64);var c,i=t(32),u=Object(i["default"])(r["default"],o["render"],o["staticRenderFns"],!1,null,null,null,!1,o["components"],c);u.options.__file="pages/mine/mine.vue",n["default"]=u.exports},60:function(e,n,t){"use strict";t.r(n);var o=t(61);t.d(n,"render",(function(){return o["render"]})),t.d(n,"staticRenderFns",(function(){return o["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return o["recyclableRender"]})),t.d(n,"components",(function(){return o["components"]}))},61:function(e,n,t){"use strict";var o;t.r(n),t.d(n,"render",(function(){return r})),t.d(n,"staticRenderFns",(function(){return c})),t.d(n,"recyclableRender",(function(){return s})),t.d(n,"components",(function(){return o}));try{o={uniIcons:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uni-icons/components/uni-icons/uni-icons")]).then(t.bind(null,182))}}}catch(i){if(-1===i.message.indexOf("Cannot find module")||-1===i.message.indexOf(".vue"))throw i;console.error(i.message),console.error("1. 排查组件名称拼写是否正确"),console.error("2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"),console.error("3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件")}var r=function(){var e=this,n=e.$createElement;e._self._c},s=!1,c=[];r._withStripped=!0},62:function(e,n,t){"use strict";t.r(n);var o=t(63),r=t.n(o);for(var s in o)["default"].indexOf(s)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(s);n["default"]=r.a},63:function(e,n,t){"use strict";(function(e,o){var r=t(4);Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var s=r(t(37)),c=r(t(39)),i=t(47),u={data:function(){return{hasLogin:!1,userInfo:{},wxQuery:{code:"",telCode:"",avatarUrl:"",nickName:""},count:0,outPutShow:!1}},onLoad:function(){},onShow:function(){this.checkLoginStatus(),console.log("1111111")},methods:{handlerOutPut:function(){this.count+=1,this.outPutShow=this.count>16&&!0},clearStroage:function(){e.clearStorage(),e.reLaunch({url:"/pages/index/index"})},handlerRecord:function(n){switch(n){case 1:this.hasLogin?e.navigateTo({url:"/bussinessPages/tjRecordPages/tjRecordPages"}):e.showToast({title:"请先登录再进行操作",icon:"none"});break;default:e.showToast({title:"升级中",icon:"none"})}},checkLoginStatus:function(){try{var n=e.getStorageSync("userInfo"),t=e.getStorageSync("userToken");t?(this.userInfo=JSON.parse(n),this.hasLogin=!0):(this.userInfo={},this.hasLogin=!1)}catch(o){console.error("获取登录状态失败",o)}},wxLogin:function(){var n=this;e.login({provider:"weixin",success:function(e){console.log("微信登录成功1",e),n.wxQuery.code=e.code,n.getUserInfo(e.code)},fail:function(n){console.error("微信登录失败",n),e.hideLoading(),e.showToast({title:"登录失败，请重试",icon:"none"})}})},wxLoginGetCode:function(){o.login({success:function(){var e=(0,c.default)(s.default.mark((function e(n){return s.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n.code?console.log("wxCode1",n.code):console.log("登录失败！"+n.errMsg);case 1:case"end":return e.stop()}}),e)})));function n(n){return e.apply(this,arguments)}return n}()})},handlerWxAvatar:function(e){console.log("微信头像",e)},getUserInfo:function(n){var t=this;e.getUserInfo({provider:"weixin",success:function(){var o=(0,c.default)(s.default.mark((function o(r){return s.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:console.log("获取用户信息成功",r),{nickName:r.userInfo.nickName,avatarUrl:r.userInfo.avatarUrl,gender:r.userInfo.gender,code:n},t.wxQuery.avatarUrl=r.userInfo.avatarUrl,t.wxQuery.nickName=r.userInfo.nickName;try{(0,i.wxLoginByCode)(t.wxQuery).then(function(){var n=(0,c.default)(s.default.mark((function n(o){var r;return s.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log("登录获取信息",o),t.hasLogin=!0,e.setStorageSync("userToken",o),e.hideLoading(),n.next=6,(0,i.getUserInfo)();case 6:r=n.sent,e.setStorageSync("userInfo",JSON.stringify(r)),t.userInfo=r;case 9:case"end":return n.stop()}}),n)})));return function(e){return n.apply(this,arguments)}}())}catch(u){console.error("保存用户信息失败",u),e.hideLoading()}case 5:case"end":return o.stop()}}),o)})));function r(e){return o.apply(this,arguments)}return r}(),fail:function(n){console.error("获取用户信息失败",n),e.hideLoading(),n.errMsg.indexOf("auth deny")>=0||n.errMsg.indexOf("auth denied")>=0?e.showModal({title:"提示",content:"需要您授权才能继续使用，是否重新授权？",success:function(n){n.confirm&&e.openSetting({success:function(e){console.log("设置页面成功打开",e)}})}}):e.showToast({title:"获取用户信息失败，请重试",icon:"none"})}})},onGetPhoneNumber:function(e){var n=this;return(0,c.default)(s.default.mark((function t(){return s.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:console.log("获取手机号-111",e),"getPhoneNumber:fail user deny"==e.detail.errMsg?console.log("点击了拒绝！！！"):(n.wxQuery.telCode=e.detail.code,n.sendCodeLogin(n.wxQuery));case 2:case"end":return t.stop()}}),t)})))()},sendCodeLogin:function(e){var n=this;return(0,c.default)(s.default.mark((function e(){return s.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:n.wxLogin();case 1:case"end":return e.stop()}}),e)})))()},logout:function(){var n=this;e.showModal({title:"提示",content:"确定要退出登录吗？",success:function(t){t.confirm&&(e.removeStorageSync("userInfo"),e.removeStorageSync("userToken"),n.userInfo={},n.hasLogin=!1,e.showToast({title:"已退出登录",icon:"success"}))}})},jumpPage:function(n){switch(n){case 1:e.navigateTo({url:"/bussinessPages/updateMine/updateMine"});break;case 2:e.navigateTo({url:"/bussinessPages/setting/setting"});break;default:break}}}};n.default=u}).call(this,t(2)["default"],t(1)["default"])},64:function(e,n,t){"use strict";t.r(n);var o=t(65),r=t.n(o);for(var s in o)["default"].indexOf(s)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(s);n["default"]=r.a},65:function(e,n,t){}},[[58,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/mine/mine.js.map