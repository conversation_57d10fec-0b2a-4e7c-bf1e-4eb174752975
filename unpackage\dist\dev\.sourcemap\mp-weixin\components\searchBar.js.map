{"version": 3, "sources": ["webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/searchBar.vue?a487", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/searchBar.vue?c9ac", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/searchBar.vue?236e", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/searchBar.vue?3a13", "uni-app:///components/searchBar.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/components/searchBar.vue?36f0"], "names": ["renderjs", "component", "options", "__file", "components", "uniIcons", "e", "message", "indexOf", "console", "error", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "data", "searchKeyword", "showStreetDropdown", "selectedStreet", "text", "value", "streetList", "methods", "toggleStreetDropdown", "selectStreet", "handleSearch", "keyword", "street", "created"], "mappings": "qHAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,KACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,2BACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IACEA,EAAa,CACXC,SAAU,WACR,OAAO,yHAKX,MAAOC,GACP,IAC+C,IAA7CA,EAAEC,QAAQC,QAAQ,wBACa,IAA/BF,EAAEC,QAAQC,QAAQ,QAWlB,MAAMF,EATNG,QAAQC,MAAMJ,EAAEC,SAChBE,QAAQC,MAAM,mBACdD,QAAQC,MACN,uFAEFD,QAAQC,MACN,mDAMN,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCjCvB,yHAAmvB,eAAG,G,mGCgCtvB,Y,EACA,CACAC,gBACA,OACAC,iBACAC,sBACAC,gBACAC,YACAC,SAEAC,aACAF,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,SAEA,CACAD,YACAC,aAKAE,SAEAC,gCACA,kDAIAC,yBACA,sBACA,2BACA,0BAIAC,wBAEA,uBACAC,2BACAC,+BAIAC,mBAAA,YACA,mDACAzB,yBACA,gCACA,OACAgB,iBACAC,sBAGA,sBACAD,YACAC,gBAIA,a,iCC9HA,yHAAwjC,eAAG,G", "file": "components/searchBar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./searchBar.vue?vue&type=template&id=c2b835c0&\"\nvar renderjs\nimport script from \"./searchBar.vue?vue&type=script&lang=js&\"\nexport * from \"./searchBar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./searchBar.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/searchBar.vue\"\nexport default component.exports", "export * from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./searchBar.vue?vue&type=template&id=c2b835c0&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./searchBar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./searchBar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"search-container\">\r\n\t\t<!-- 返回按钮和搜索区域 -->\r\n\t\t<view class=\"search-header\">\r\n\t\t\t<!-- 街道选择下拉菜单 -->\r\n\t\t\t<view class=\"dropdown-area\">\r\n\t\t\t\t<view class=\"dropdown-trigger\" @click=\"toggleStreetDropdown\">\r\n\t\t\t\t\t<text class=\"dropdown-text\">{{selectedStreet.text}}</text>\r\n\t\t\t\t\t<uni-icons type=\"bottom\" size=\"14\" color=\"#000\"></uni-icons>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"dropdown-menu\" v-if=\"showStreetDropdown\">\r\n\t\t\t\t\t<view class=\"dropdown-item\" v-for=\"(item, index) in streetList\" :key=\"index\"\r\n\t\t\t\t\t\t@click=\"selectStreet(item)\">\r\n\t\t\t\t\t\t<text>{{ item.text }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 搜索输入框 -->\r\n\t\t\t<view class=\"search-input-area\">\r\n\t\t\t\t<input type=\"text\" class=\"search-input\" placeholder=\"请输入关键字\" v-model=\"searchKeyword\" />\r\n\t\t\t</view>\r\n\r\n\t\t\t<!-- 搜索按钮 -->\r\n\t\t\t<view class=\"search-btn\" @click=\"handleSearch\">\r\n\t\t\t\t<text>搜索</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport { getDictionary } from \"@/api/rs.js\"\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsearchKeyword: '',\r\n\t\t\t\tshowStreetDropdown: false,\r\n\t\t\t\tselectedStreet: {\r\n\t\t\t\t\ttext: '全部街道',\r\n\t\t\t\t\tvalue: 0\r\n\t\t\t\t},\r\n\t\t\t\tstreetList: [{\r\n\t\t\t\t\t\ttext: '全部街道',\r\n\t\t\t\t\t\tvalue: 0\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '东海街道',\r\n\t\t\t\t\t\tvalue: 1\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '城东街道',\r\n\t\t\t\t\t\tvalue: 2\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '城西街道',\r\n\t\t\t\t\t\tvalue: 3\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '北峰街道',\r\n\t\t\t\t\t\tvalue: 4\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '华大街道',\r\n\t\t\t\t\t\tvalue: 5\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '东湖街道',\r\n\t\t\t\t\t\tvalue: 6\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '丰泽街道',\r\n\t\t\t\t\t\tvalue: 7\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '泉秀街道',\r\n\t\t\t\t\t\tvalue: 8\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '清源街道',\r\n\t\t\t\t\t\tvalue: 9\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttext: '临海街道',\r\n\t\t\t\t\t\tvalue: 10\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 切换街道下拉菜单显示状态\r\n\t\t\ttoggleStreetDropdown() {\r\n\t\t\t\tthis.showStreetDropdown = !this.showStreetDropdown;\r\n\t\t\t},\r\n\r\n\t\t\t// 选择街道\r\n\t\t\tselectStreet(street) {\r\n\t\t\t\tthis.selectedStreet = street;\r\n\t\t\t\tthis.showStreetDropdown = false;\r\n\t\t\t\tthis.$emit(\"onSelect\", street)\r\n\t\t\t},\r\n\r\n\t\t\t// 处理搜索\r\n\t\t\thandleSearch() {\r\n\t\t\t\t// 触发搜索事件，将搜索关键词和选中的街道传递给父组件\r\n\t\t\t\tthis.$emit('onSearch', {\r\n\t\t\t\t\tkeyword: this.searchKeyword,\r\n\t\t\t\t\tstreet: this.selectedStreet\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tgetDictionary(\"zeroStreet\").then((res) => {\r\n\t\t\t\tconsole.log(\"actions\", res)\r\n\t\t\t\tthis.streetList = res.map(el => {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\ttext: el.entryName,\r\n\t\t\t\t\t\tvalue: el.entryCode\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\tthis.streetList.unshift({\r\n\t\t\t\t\ttext: '全部街道',\r\n\t\t\t\t\tvalue: ''\r\n\t\t\t\t});\r\n\t\t\t});\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style>\r\n\t.search-container {\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t.search-header {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #fff;\r\n\t\tpadding: 10px;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.back-btn {\r\n\t\twidth: 30px;\r\n\t\theight: 30px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.dropdown-area {\r\n\t\tposition: relative;\r\n\t\tmargin-right: 10px;\r\n\t}\r\n\r\n\t.dropdown-trigger {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 5px;\r\n\t\theight: 36px;\r\n\t}\r\n\r\n\t.dropdown-text {\r\n\t\tcolor: #000;\r\n\t\tfont-size: 14px;\r\n\t\tmargin-right: 5px;\r\n\t}\r\n\r\n\t.dropdown-menu {\r\n\t\tposition: absolute;\r\n\t\ttop: 40px;\r\n\t\tleft: 0;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 4px;\r\n\t\tbox-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\r\n\t\tz-index: 100;\r\n\t\twidth: 120px;\r\n\t}\r\n\r\n\t.dropdown-item {\r\n\t\tpadding: 10px 15px;\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #333;\r\n\t\tborder-bottom: 1px solid #f5f5f5;\r\n\t}\r\n\r\n\t.dropdown-item:last-child {\r\n\t\tborder-bottom: none;\r\n\t}\r\n\r\n\t.search-input-area {\r\n\t\tflex: 1;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 4px;\r\n\t\theight: 36px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.search-input {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tfont-size: 14px;\r\n\t}\r\n\r\n\t.search-btn {\r\n\t\twidth: 60px;\r\n\t\theight: 36px;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 4px;\r\n\t\tmargin-left: 10px;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.search-btn text {\r\n\t\tcolor: #4a89dc;\r\n\t\tfont-size: 14px;\r\n\t\tfont-weight: bold;\r\n\t}\r\n</style>", "import mod from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./searchBar.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./searchBar.vue?vue&type=style&index=0&lang=css&\""], "sourceRoot": ""}