<view data-event-opts="{{[['tap',[['handlerOutPut',['$event']]]]]}}" class="mine-container" bindtap="__e"><view class="user-info-section custom-nav"><block wx:if="{{!hasLogin}}"><view class="user-info-wrapper"><view class="avatar-placeholder"><block wx:if="{{!userInfo.avatarUrl}}"><view class="avatar-null"><uni-icons vue-id="bae1c640-1" type="person" size="50" color="#CCCCCC" bind:__l="__l"></uni-icons></view></block><block wx:else><image class="avatar" src="{{userInfo.avatarUrl}}" mode></image></block></view><button class="login-btn" type="text" open-type="getPhoneNumber" data-event-opts="{{[['getphonenumber',[['onGetPhoneNumber',['$event']]]]]}}" bindgetphonenumber="__e">微信登录</button></view></block><block wx:else><view class="user-info-wrapper"><image class="avatar" src="{{userInfo.avatarUrl||'/static/default-avatar.png'}}"></image><view class="user-detail"><view class="nickname">{{userInfo.nickName||'微信用户'}}</view><view data-event-opts="{{[['tap',[['jumpPage',[1]]]]]}}" class="float-desc" bindtap="__e">个人信息<uni-icons vue-id="bae1c640-2" type="right" size="16" color="#999999" bind:__l="__l"></uni-icons></view></view></view></block></view><view class="menu-section"><view class="menu-group"><view data-event-opts="{{[['tap',[['handlerRecord',[1]]]]]}}" class="menu-item" bindtap="__e"><image class="icon-box" src="../../static/icons/records.png" mode></image><text class="menu-text">提交记录</text><uni-icons vue-id="bae1c640-3" type="right" size="16" color="#CCCCCC" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['handlerRecord',[0]]]]]}}" class="menu-item" bindtap="__e"><uni-icons vue-id="bae1c640-4" type="tune-filled" size="16" color="#4989FD" bind:__l="__l"></uni-icons><text class="menu-text">发布求职信息</text><uni-icons vue-id="bae1c640-5" type="right" size="16" color="#CCCCCC" bind:__l="__l"></uni-icons></view><view data-event-opts="{{[['tap',[['jumpPage',[2]]]]]}}" class="menu-item" bindtap="__e"><image class="icon-box" src="../../static/icons/setting.png" mode></image><text class="menu-text">设置</text><uni-icons vue-id="bae1c640-6" type="right" size="16" color="#CCCCCC" bind:__l="__l"></uni-icons></view><block wx:if="{{outPutShow}}"><view data-event-opts="{{[['tap',[['clearStroage',['$event']]]]]}}" class="menu-item" bindtap="__e"><image class="icon-box" src="../../static/icons/setting.png" mode></image><text class="menu-text">清除缓存</text><uni-icons vue-id="bae1c640-7" type="right" size="16" color="#CCCCCC" bind:__l="__l"></uni-icons></view></block></view></view></view>