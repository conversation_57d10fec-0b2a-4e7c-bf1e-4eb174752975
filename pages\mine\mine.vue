<template>
	<view class="mine-container" @click="handlerOutPut">
		<!-- 顶部背景 -->
		<view class="user-info-section custom-nav">
			<!-- 未登录状态 -->
			<view class="user-info-wrapper" v-if="!hasLogin">
				<view class="avatar-placeholder">
					<view class="avatar-null" v-if="!userInfo.avatarUrl">
						<uni-icons type="person" size="50" color="#CCCCCC"></uni-icons>
					</view>
					<image class="avatar" v-else :src="userInfo.avatarUrl" mode=""></image>
				</view>
				<button type="text" class="login-btn" open-type="getPhoneNumber"
					@getphonenumber="onGetPhoneNumber">微信登录</button>
				<!-- <button open-type="chooseAvatar" @chooseavatar="handlerWxAvatar">获取头像</button> -->
				<!-- #ifdef MP-WEIXIN -->
				<!-- #endif -->
				<!-- #ifndef MP-WEIXIN -->
				<!-- <button v-if="current === 0" class="btn btn_primary btn_login top_32">快捷登录请在微信小程序中打开</button> -->
				<!-- #endif -->
			</view>

			<!-- 已登录状态 -->
			<view class="user-info-wrapper" v-else>
				<image class="avatar" :src="userInfo.avatarUrl || '/static/default-avatar.png'"></image>
				<view class="user-detail">
					<view class="nickname">{{userInfo.nickName || '微信用户'}}</view>
					<view class="float-desc" @click="jumpPage(1)">个人信息 <uni-icons type="right" size="16"
							color="#999999"></uni-icons></view>
				</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-group">
				<view class="menu-item" @click="handlerRecord(1)">
					<image src="../../static/icons/records.png" class="icon-box" mode=""></image>
					<text class="menu-text">提交记录</text>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
				<view v-if="false" class="menu-item" @click="handlerRecord(0)">
					<uni-icons type="tune-filled" size="16" color="#4989FD"></uni-icons>
					<text class="menu-text">发布求职信息</text>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
				<!-- 				<view class="menu-item" @click="jumpPage(1)">
					<uni-icons type="chat" size="24" color="#4989FD"></uni-icons>
					<text class="menu-text">个人信息</text>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view> -->
				<view class="menu-item" @click="jumpPage(2)">
					<image src="../../static/icons/setting.png" class="icon-box" mode=""></image>
					<text class="menu-text">设置</text>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
				<view class="menu-item" v-if="outPutShow" @click="clearStroage">
					<image src="../../static/icons/setting.png" class="icon-box" mode=""></image>
					<text class="menu-text">清除缓存</text>
					<uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
				</view>
			</view>

			<!--      <view class="menu-group">
        <view class="menu-item">
          <uni-icons type="gear" size="24" color="#4989FD"></uni-icons>
          <text class="menu-text">设置</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
        <view class="menu-item">
          <uni-icons type="help" size="24" color="#4989FD"></uni-icons>
          <text class="menu-text">帮助与反馈</text>
          <uni-icons type="right" size="16" color="#CCCCCC"></uni-icons>
        </view>
      </view> -->

			<!-- 退出登录 按钮 -->
			<!-- 			<view class="logout-btn" v-if="hasLogin" @click="logout">
				<text>退出登录</text>
			</view> -->
		</view>
	</view>
</template>

<script>
	import {
		wxLoginByCode,
		getUserInfo
	} from "../../api/rs.js"
	export default {
		data() {
			return {
				hasLogin: false,
				userInfo: {},
				wxQuery: {
					code: '',
					telCode: '',
					avatarUrl: '',
					nickName: ''
				},
				count: 0,
				outPutShow: false
			};
		},
		onLoad() {

		},
		onShow() {
			// 页面加载时检查登录状态
			this.checkLoginStatus();
			console.log("1111111")
		},
		methods: {
			handlerOutPut() {
				this.count += 1;
				this.outPutShow = this.count > 16 && true
			},
			clearStroage() {
				uni.clearStorage()
				uni.reLaunch({
					url: '/pages/index/index'
				})
			},
			handlerRecord(type) {
				switch (type) {
					case 1: {
						if (this.hasLogin) {
							uni.navigateTo({
								url: '/bussinessPages/tjRecordPages/tjRecordPages'
							})
						} else {
							uni.showToast({
								title: '请先登录再进行操作',
								icon: 'none'
							})
						}
					}
					break;
					default: {
						uni.showToast({
							title: '升级中',
							icon: 'none'
						})
					}
				}
			},
			// 检查登录状态
			checkLoginStatus() {
				try {
					const userInfo = uni.getStorageSync('userInfo');
					const userToken = uni.getStorageSync('userToken')
					if (userToken) {
						this.userInfo = JSON.parse(userInfo);
						this.hasLogin = true
					} else {
						// this.wxLogin()
						this.userInfo = {};
						this.hasLogin = false;
					}
				} catch (e) {
					console.error('获取登录状态失败', e);
				}
			},
			// 微信登录
			wxLogin() {
				// 调用微信登录接口
				uni.login({
					provider: 'weixin',
					success: (loginRes) => {
						console.log('微信登录成功1', loginRes);
						this.wxQuery.code = loginRes.code
						// 获取用户信息
						this.getUserInfo(loginRes.code);
					},
					fail: (err) => {
						console.error('微信登录失败', err);
						uni.hideLoading();
						uni.showToast({
							title: '登录失败，请重试',
							icon: 'none'
						});
					}
				});
			},
			// 微信获取用户code
			// 微信登录--获取凭证信息
			wxLoginGetCode() {
				let _ = this
				wx.login({
					success: async (res) => {
						if (res.code) { //微信登录成功 已拿到code  
							// this.jsCode=res.code        //保存获取到的code  
							console.log("wxCode1", res.code)
							//           uni.request({  
							//               url: 'https://api.weixin.qq.com/sns/jscode2session',  
							//               method:'GET',  
							//               data: {  
							//                   appid: 'wx56403276d533a62a',        //你的小程序的APPID  
							//                   secret: 'xxxxxxxxxx',       //你的小程序的secret,  
							//                   code: res.code              //wx.login 登录成功后的code  
							//               },  
							//               success: (ctx) => {  
							//                   // 换取成功后 暂存这些数据 留作后续操作  
							//                   // this.openid=cts.data.openid     //openid 用户唯一标识  
							//                   // this.unionid=cts.data.unionid     //unionid 开放平台唯一标识  
							//                   // this.session_key=cts.data.session_key     //session_key  会话密钥  
							// console.log("ctx",ctx)
							//               }  
							//           });  
						} else {
							console.log('登录失败！' + res.errMsg)
						}
					}
				})
			},
			// 获取微信头像
			handlerWxAvatar(res) {
				console.log("微信头像", res)
			},

			// 获取用户信息
			getUserInfo(code) {
				uni.getUserInfo({
					provider: 'weixin',
					success: async (infoRes) => {
						console.log('获取用户信息成功', infoRes);

						// 构建用户信息对象
						const userInfo = {
							nickName: infoRes.userInfo.nickName,
							avatarUrl: infoRes.userInfo.avatarUrl,
							gender: infoRes.userInfo.gender,
							code: code, // 登录凭证
						};
						this.wxQuery.avatarUrl = infoRes.userInfo.avatarUrl;
						this.wxQuery.nickName = infoRes.userInfo.nickName
						// 保存用户信息到本地
						try {

							wxLoginByCode(this.wxQuery).then(async res => {
								console.log("登录获取信息", res)
								this.hasLogin = true
								uni.setStorageSync('userToken', res)
								// 这里可以调用后端接口，将code发送到服务器换取openid等信息
								// this.sendCodeToServer(code, userInfo);
								uni.hideLoading();
								const userInfo = await getUserInfo()
								uni.setStorageSync('userInfo', JSON.stringify(userInfo));
								this.userInfo = userInfo;
							})

						} catch (e) {
							console.error('保存用户信息失败', e);
							uni.hideLoading();
						}
					},
					fail: (err) => {
						console.error('获取用户信息失败', err);
						uni.hideLoading();

						// 用户拒绝授权
						if (err.errMsg.indexOf('auth deny') >= 0 || err.errMsg.indexOf('auth denied') >= 0) {
							uni.showModal({
								title: '提示',
								content: '需要您授权才能继续使用，是否重新授权？',
								success: (res) => {
									if (res.confirm) {
										// 打开设置页面让用户手动授权
										uni.openSetting({
											success: (settingRes) => {
												console.log('设置页面成功打开', settingRes);
											}
										});
									}
								}
							});
						} else {
							uni.showToast({
								title: '获取用户信息失败，请重试',
								icon: 'none'
							});
						}
					}
				});
			},
			async onGetPhoneNumber(e) {
				console.log("获取手机号-111", e)
				if (e.detail.errMsg == "getPhoneNumber:fail user deny") { //用户决绝授权  
					console.log("点击了拒绝！！！")
					//拒绝授权后弹出一些提示  

				} else { //允许授权 
					this.wxQuery.telCode = e.detail.code
					this.sendCodeLogin(this.wxQuery)
				}
			},
			// 登录
			async sendCodeLogin(data) {
				this.wxLogin()
			},
			// 退出登录
			logout() {
				uni.showModal({
					title: '提示',
					content: '确定要退出登录吗？',
					success: (res) => {
						if (res.confirm) {
							// 清除本地存储的用户信息
							uni.removeStorageSync('userInfo');
							uni.removeStorageSync('userToken');
							this.userInfo = {};
							this.hasLogin = false;

							uni.showToast({
								title: '已退出登录',
								icon: 'success'
							});
						}
					}
				});
			},
			// 选项跳转页
			jumpPage(type) {
				switch (type) {
					case 1:
						uni.navigateTo({
							url: '/bussinessPages/updateMine/updateMine'
						})
						break;
					case 2:
						uni.navigateTo({
							url: '/bussinessPages/setting/setting'
						})
						break;
					default:
						break;
				}
			}
		}
	};
</script>

<style>
	.mine-container {
		display: flex;
		flex-direction: column;
		/* min-height: 100vh; */
		background-color: #F6F9FF;
		height: 95vh;
	}

	.user-info-section {
		background: url('data:image/png;base64,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');
		background-repeat: no-repeat;
		background-size: 100%;
		height: 414upx;
		position: relative;
		box-sizing: border-box;
	}

	.user-info-wrapper {
		display: flex;
		width: 702upx;
		height: 108upx;
		align-items: center;
		background: #FFFFFF;
		box-shadow: 0upx 4upx 12upx 0upx rgba(0, 0, 0, 0.04);
		border-radius: 16upx;
		position: absolute;
		left: 50%;
		transform: translateX(-50%);
		bottom: -3%
	}

	.avatar-placeholder {
		/* 		width: 160upx;
		height: 160upx; */
		border-radius: 50%;
		background-color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 30upx;
	}

	.avatar-null {
		width: 128upx;
		height: 128upx;
		border-radius: 32upx;
		border: 4rpx solid #FFFFFF;
		/* margin-right: 30upx; */
		position: absolute;
		left: 6%;
		top: -45%;
		z-index: 999;
		background-color: #fff;
		display: flex;
		justify-content: center;
		align-items: center;
	}

	.avatar {
		width: 128upx;
		height: 128upx;
		border-radius: 32upx;
		border: 4rpx solid #FFFFFF;
		/* margin-right: 30upx; */
		position: absolute;
		left: 6%;
		top: -45%;
		z-index: 999;
	}

	.icon-box {
		width: 32upx;
		height: 32upx;
		object-fit: contain
	}

	.user-detail {
		display: flex;
		flex-direction: column;
		font-weight: 400;
		font-size: 28upx;
		color: #999999;
	}

	.float-desc {
		position: absolute;
		right: 24upx;
		bottom: 37%;
		display: flex;
		align-items: center;
	}

	.nickname {
		color: #333333;
		font-weight: 600;
		font-size: 36upx;
		/* 		margin-bottom: 10upx; */
		position: absolute;
		left: 36%;
		bottom: 31%;
		width: 244upx;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.user-id {
		font-size: 28upx;
		color: rgba(255, 255, 255, 0.8);
	}

	.login-btn {
		background-color: #9BC7FF;
		color: #FFF;
		font-size: 32upx;
		padding: 0 40upx;
		border-radius: 40upx;
		font-weight: bold;
		border: none;
	}

	.menu-section {
		flex: 1;
		padding: 30upx;
		/* box-shadow: 0rpx 4rpx 12rpx 0rpx rgba(0, 0, 0, 0.04); */
		background-color: #F6F9FF;
		height: 100%;
	}

	.menu-group {
		background-color: #ffffff;
		border-radius: 20upx;
		margin-bottom: 30upx;
		overflow: hidden;
		border-radius: 16upx;
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 30upx;
		/* border-bottom: 1px solid #f5f5f5; */
	}

	.menu-item:last-child {
		border-bottom: none;
	}

	.menu-text {
		flex: 1;
		margin-left: 20upx;
		font-size: 32upx;
		color: #333333;
	}

	.logout-btn {
		background-color: #ffffff;
		border-radius: 20upx;
		padding: 30upx;
		text-align: center;
		margin-top: 60upx;
	}

	.logout-btn text {
		color: #ff5a5f;
		font-size: 32upx;
		font-weight: bold;
	}
</style>