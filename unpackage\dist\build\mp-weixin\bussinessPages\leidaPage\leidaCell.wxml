<view data-event-opts="{{[['tap',[['$emit',['onDetail','$0'],['obj']]]]]}}" class="company-cell data-v-789604f0" bindtap="__e"><view class="header-cell data-v-789604f0"><view class="company-title over-text data-v-789604f0">{{obj.firmName}}</view><view class="detail-link data-v-789604f0">详情<uni-icons vue-id="2a4ef098-1" type="right" color="#999999" size="16" class="data-v-789604f0" bind:__l="__l"></uni-icons></view></view><view class="company-info data-v-789604f0"><view class="info-item data-v-789604f0"><uni-icons vue-id="2a4ef098-2" type="person-filled" color="#f56c6c" size="16" class="data-v-789604f0" bind:__l="__l"></uni-icons><text class="over-text data-v-789604f0">{{obj.postNameToString||'-'}}</text></view><view class="info-item data-v-789604f0"><uni-icons vue-id="2a4ef098-3" type="phone-filled" color="#4a89dc" size="16" class="data-v-789604f0" bind:__l="__l"></uni-icons><text class="over-text data-v-789604f0">{{obj.contactTel||'-'}}</text></view><view class="info-item data-v-789604f0"><uni-icons vue-id="2a4ef098-4" type="location-filled" color="#4a89dc" size="16" class="data-v-789604f0" bind:__l="__l"></uni-icons><text class="over-text data-v-789604f0">{{obj.firmAddress||'-'}}</text></view></view></view>