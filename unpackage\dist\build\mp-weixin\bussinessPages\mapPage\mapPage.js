(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/mapPage/mapPage"],{"0159":function(e,t,n){},"5e01":function(e,t,n){"use strict";(function(e,t){var o=n("47a9");n("bfec");o(n("3240"));var r=o(n("ce7d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"816d":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return o}));var o={liuEasyMap:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map")]).then(n.bind(null,"e2ea"))}},r=function(){var e=this.$createElement;this._self._c},a=[]},8258:function(e,t,n){"use strict";n.r(t);var o=n("86ee"),r=n.n(o);for(var a in o)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(a);t["default"]=r.a},"86ee":function(e,t,n){"use strict";(function(e){var o=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=o(n("7eb4")),a=o(n("7ca3")),i=o(n("ee10")),c=n("3ec1"),s=n("2823");function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach((function(t){(0,a.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var f={data:function(){return{markerData:[],polygons:[{points:[{latitude:"24.931423",longitude:"118.649744"}],strokeWidth:2,strokeColor:"#FF000060",fillColor:"#FF000090"}],closeIcon:n("0880"),markerImgIn:n("e409"),goImgIn:n("1fc1"),centerTar:{centerLat:"24.931423",centerLng:"118.649744"},body:{substreet:"",page:1,pageSize:10,total:0,status:0},streetLists:s.streetLists,finished:!1,isUseMyLocation:!1}},components:{searchBarVue:function(){n.e("components/searchBar").then(function(){return resolve(n("536b"))}.bind(null,n)).catch(n.oe)}},methods:{handlerAreaSelect:function(e){console.log("选11111111择区域",e.text),this.body.substreet="全部街道"==e.text?"":e.text;var t=this.streetLists[e.text];console.log("选择区域",e,t),this.streetPoint={latitude:t.latitude,centerLng:t.longitude},this.centerTar.centerLat=t.latitude,this.centerTar.centerLng=t.longitude,this.refresh()},handlerSearch:function(e){console.log("搜索",e),this.body.serviceName=e.keyword,this.refresh()},refresh:function(){this.markerData=[],this.finished=!1,this.body.page=1,this.fetchMapLists()},regionchange:function(){this.finished||this.fetchMapLists()},fetchMapLists:function(){var e=this;return(0,i.default)(r.default.mark((function t(){var n,o,a,i,s,l;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return null===(n=e.$refs)||void 0===n||null===(o=n.liuEasyMap)||void 0===o||o.clearMarker(),a=e.body,t.next=4,(0,c.getServiceLists)(a);case 4:i=t.sent,console.log("pageConfig",i),[],e.body.total=i.total,s=i.records,e.markerData=e.markerData.concat(s),(e.markerData.length>e.body.total||e.markerData.length===e.body.total)&&(e.finished=!0),e.markerData=e.markerData.map((function(e,t){return u(u({},e),{},{id:t,name:e.serviceName,address:e.serviceAddress,latitude:e.lat,longitude:e.lon,calloutColor:"#ffffff",calloutFontSize:14,calloutBorderRadius:6,calloutPadding:8,calloutBgColor:"#0B6CFF",calloutDisplay:"ALWAYS"})})),e.markerData&&(l=e.markerData.filter((function(e){return e.latitude})),e.isUseMyLocation||(e.centerTar={centerLat:l.length?l[0].latitude:e.streetPoint.latitude,centerLng:l.length?l[0].longitude:e.streetPoint.centerLng})),console.log("mockLats",e.markerData);case 15:case"end":return t.stop()}}),t)})))()},markerClick:function(e){console.log("点击标记点信息1：",e)},getLocationCurrent:function(){var e=this;if(console.log("_.dataList",e.markerData),e.markerData.length){var t=e.markerData[0];t.placeLatitude?(e.centerTar.centerLat=t.placeLatitude,e.centerTar.centerLng=t.placeLongitude):(console.log("7666666666"),this.getLocation())}else console.log("7666666666"),this.getLocation()},getWebLocation:function(){var t=this;e.getLocation({type:"wgs84",success:function(e){console.log("当前位置的经度：",e),t.centerTar.centerLat=e.latitude,t.centerTar.centerLng=e.longitude,this.isUseMyLocation=!0},fail:function(e){console.log("错误：",e),this.isUseMyLocation=!1}})},getLocation:function(){var t=this,n=this;e.getLocation({type:"wgs84",success:function(e){console.log("当前位置的经度："+e.longitude),console.log("当前位置的纬度："+e.latitude),n.centerTar.centerLng=e.longitude,n.centerTar.centerLat=e.latitude,t.isUseMyLocation=!0,console.log(e,"根据经纬度获取地址")},fail:function(n){e.showModal({content:"检测到您没打开获取位置功能权限，是否去设置打开？",confirmText:"确认",cancelText:"取消",success:function(n){n.confirm?e.openSetting({success:function(t){e.showToast({title:"授权后请重新打开此页面",icon:"none"})},fail:function(e){console.log(e)}}):e.showToast({title:"获取地理位置授权失败",icon:"none",success:function(){t.isUseMyLocation=!1}})}})}})}},onLoad:function(){this.refresh()},onShow:function(){this.getLocationCurrent()}};t.default=f}).call(this,n("df3c")["default"])},9887:function(e,t,n){"use strict";var o=n("0159"),r=n.n(o);r.a},ce7d:function(e,t,n){"use strict";n.r(t);var o=n("816d"),r=n("8258");for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(a);n("9887");var i=n("828b"),c=Object(i["a"])(r["default"],o["b"],o["c"],!1,null,"20b141e9",null,!1,o["a"],void 0);t["default"]=c.exports}},[["5e01","common/runtime","common/vendor"]]]);