@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.notice.data-v-31563cfc {
  height: 80rpx;
  line-height: 80rpx;
  margin: 0 3%;
  margin-top: 15rpx;
  padding: 0 10rpx;
  box-shadow: 0rpx 0rpx 10rpx #eee;
  border-radius: 32rpx;
  background: #fff;
}
.detail-txt.data-v-31563cfc {
  font-weight: 400;
  font-size: 24rpx;
  color: #FFAE00;
  display: flex;
  align-items: center;
}
.left_icon.data-v-31563cfc {
  width: 10%;
  height: 24px;
  float: left;
}
.left_icon .iconfont.data-v-31563cfc {
  display: inline-block;
  font-size: 44rpx;
}
.right_notice.data-v-31563cfc {
  float: left;
  width: 90%;
}
.right_notice .notice_swiper.data-v-31563cfc {
  height: 80rpx;
}
.notice_swiper .sw_item.data-v-31563cfc {
  height: 80rpx;
}
.notice_swiper .sw_item .sw_text.data-v-31563cfc {
  font-size: 24rpx;
  color: #333;
  display: inline-block;
  width: 81%;
}
.notice_swiper .sw_image.data-v-31563cfc {
  width: 90rpx;
  height: 40rpx;
  float: right;
}
