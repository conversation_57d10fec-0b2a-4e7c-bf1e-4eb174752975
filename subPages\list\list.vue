<template>
	<view class="list-container">
		<!-- 导航栏 -->
		<uni-nav-bar 
			:fixed="true" 
			:shadow="true" 
			:border="false"
			background-color="#007AFF"
			color="#FFFFFF"
			title="列表示例"
			left-icon="left"
			@clickLeft="goBack"
		/>
		
		<!-- 搜索栏 -->
		<view class="search-container">
			<uni-search-bar 
				v-model="searchKeyword"
				placeholder="搜索内容"
				:clearButton="true"
				@confirm="handleSearch"
				@clear="handleClear"
			/>
		</view>
		
		<!-- 列表内容 -->
		<z-paging 
			ref="paging" 
			v-model="dataList" 
			@query="queryList"
			:refresher-enabled="true"
			:auto="true"
		>
			<view class="list-content">
				<uni-card 
					v-for="(item, index) in dataList" 
					:key="index"
					:title="item.title"
					:sub-title="item.subtitle"
					:extra="item.time"
					:thumbnail="item.image"
					@click="handleItemClick(item)"
				>
					<text class="card-content">{{ item.content }}</text>
					<template v-slot:actions>
						<view class="card-actions">
							<button size="mini" type="primary" @click.stop="handleEdit(item)">编辑</button>
							<button size="mini" type="warn" @click.stop="handleDelete(item)">删除</button>
						</view>
					</template>
				</uni-card>
			</view>
		</z-paging>
		
		<!-- 浮动按钮 -->
		<uni-fab 
			:pattern="fabPattern" 
			:content="fabContent"
			@trigger="handleFabClick"
		/>
	</view>
</template>

<script>
import { getDataList, deleteData } from '@/api/rs.js'

export default {
	name: 'ListPage',
	data() {
		return {
			searchKeyword: '',
			dataList: [],
			fabPattern: {
				color: '#007AFF',
				backgroundColor: '#fff',
				selectedColor: '#007AFF'
			},
			fabContent: [
				{
					iconPath: '/static/icons/add.png',
					selectedIconPath: '/static/icons/add-active.png',
					text: '添加',
					active: false
				}
			]
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack()
		},
		
		// 查询列表数据
		async queryList(pageNo, pageSize) {
			try {
				const params = {
					page: pageNo,
					pageSize: pageSize,
					keyword: this.searchKeyword
				}
				
				const result = await getDataList(params)
				
				// 模拟数据结构
				const mockData = []
				for (let i = 0; i < pageSize; i++) {
					mockData.push({
						id: (pageNo - 1) * pageSize + i + 1,
						title: `标题 ${(pageNo - 1) * pageSize + i + 1}`,
						subtitle: `副标题 ${(pageNo - 1) * pageSize + i + 1}`,
						content: `这是第 ${(pageNo - 1) * pageSize + i + 1} 项的内容描述，可以是任意长度的文本内容。`,
						time: new Date().toLocaleString(),
						image: '/static/images/default.png'
					})
				}
				
				this.$refs.paging.complete(mockData)
			} catch (error) {
				console.error('查询列表失败：', error)
				this.$refs.paging.complete(false)
			}
		},
		
		// 搜索
		handleSearch() {
			this.$refs.paging.reload()
		},
		
		// 清空搜索
		handleClear() {
			this.searchKeyword = ''
			this.$refs.paging.reload()
		},
		
		// 列表项点击
		handleItemClick(item) {
			uni.navigateTo({
				url: `/subPages/detail/detail?id=${item.id}`
			})
		},
		
		// 编辑
		handleEdit(item) {
			uni.navigateTo({
				url: `/subPages/form/form?id=${item.id}&mode=edit`
			})
		},
		
		// 删除
		handleDelete(item) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除"${item.title}"吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							await deleteData(item.id)
							uni.showToast({
								title: '删除成功',
								icon: 'success'
							})
							this.$refs.paging.reload()
						} catch (error) {
							console.error('删除失败：', error)
							uni.showToast({
								title: '删除失败',
								icon: 'error'
							})
						}
					}
				}
			})
		},
		
		// 浮动按钮点击
		handleFabClick(e) {
			if (e.index === 0) {
				// 添加新项
				uni.navigateTo({
					url: '/subPages/form/form?mode=add'
				})
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.list-container {
	background-color: #f8f8f8;
	min-height: 100vh;
}

.search-container {
	padding: 10px 15px;
	background-color: #ffffff;
	border-bottom: 1px solid #e5e5e5;
	margin-top: 88px;
}

.list-content {
	padding: 15px;
}

.card-content {
	color: #666666;
	font-size: 14px;
	line-height: 1.5;
}

.card-actions {
	display: flex;
	justify-content: flex-end;
	gap: 10px;
	margin-top: 10px;
}
</style>
