{"version": 3, "sources": ["webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/leidaCell.vue?00d5", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/leidaCell.vue?51a2", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/leidaCell.vue?9f0b", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/leidaCell.vue?5a87", "uni-app:///bussinessPages/pldDetail/leidaCell.vue", "webpack:///D:/work-space/泉州/引征ai应用/rs-project/bussinessPages/pldDetail/leidaCell.vue?eee7"], "names": ["renderjs", "component", "options", "__file", "components", "render", "_vm", "this", "_h", "$createElement", "_self", "_c", "recyclableRender", "staticRenderFns", "_withStripped", "name", "props", "postName", "type", "default", "recruitingNumber", "postRequire", "salary", "id", "obj", "methods", "handleClick"], "mappings": "mIAAA,oIACIA,EADJ,QASIC,EAAY,qBACd,aACA,YACA,sBACA,EACA,KACA,WACA,MACA,EACA,gBACAD,GAGFC,EAAUC,QAAQC,OAAS,yCACZ,aAAAF,E,0CCvBf,uQ,iCCAA,IAAIG,EAAJ,0LACA,IAAIC,EAAS,WACX,IAAIC,EAAMC,KACNC,EAAKF,EAAIG,eACJH,EAAII,MAAMC,IAEjBC,GAAmB,EACnBC,EAAkB,GACtBR,EAAOS,eAAgB,G,iCCRvB,yHAAkwB,eAAG,G,8sBCgBrwB,CACAC,cACAC,OAEAC,UACAC,YACAC,YAGAC,kBACAF,qBACAC,WAGAE,aACAH,YACAC,YAGAG,QACAJ,qBACAC,YAGAI,IACAL,qBACAC,YAEAK,KACAN,YACAC,uBAGAM,SACAC,uBACA,sBACAH,WACAN,uBACAG,uCACAC,6BACAC,oBACA,cAIA,a,iCC7DA,yHAAqmC,eAAG,G", "file": "bussinessPages/pldDetail/leidaCell.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./leidaCell.vue?vue&type=template&id=73456b22&scoped=true&\"\nvar renderjs\nimport script from \"./leidaCell.vue?vue&type=script&lang=js&\"\nexport * from \"./leidaCell.vue?vue&type=script&lang=js&\"\nimport style0 from \"./leidaCell.vue?vue&type=style&index=0&id=73456b22&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"73456b22\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"bussinessPages/pldDetail/leidaCell.vue\"\nexport default component.exports", "export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=template&id=73456b22&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"job-card\" @click=\"handleClick\">\n    <view class=\"job-title\">{{ obj.postName || '-' }}：{{ obj.recruitingNumber || '0' }}名</view>\n    <view class=\"job-desc\">\n      <view class=\"job-item\">\n        <rich-text :nodes=\"obj.postRequire\"></rich-text>\n      </view>\n    </view>\n    <view class=\"job-salary\">\n      <text class=\"salary-amount\">{{ obj.salary || '0' }}</text>\n      <text class=\"salary-unit\">元/月</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  name: 'GwCell',\n  props: {\n    // 职位名称\n    postName: {\n      type: String,\n      default: ''\n    },\n    // 招聘人数\n    recruitingNumber: {\n      type: [Number, String],\n      default: 0\n    },\n    // 职位要求\n    postRequire: {\n      type: String,\n      default: ''\n    },\n    // 薪资\n    salary: {\n      type: [Number, String],\n      default: ''\n    },\n    // 职位ID\n    id: {\n      type: [Number, String],\n      default: ''\n    },\r\n\tobj: {\r\n\t\ttype: Object,\r\n\t\tdefault: () => {}\r\n\t}\n  },\n  methods: {\n    handleClick() {\n      this.$emit('click', {\n        id: this.id,\n        postName: this.postName,\n        recruitingNumber: this.recruitingNumber,\n        postRequire: this.postRequire,\n        salary: this.salary,\r\n\t\t...this.obj\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.job-card {\n  background-color: #fff;\n  border-radius: 10px;\n  margin: 15upx 0 0 !important;\n  padding: 0 30upx 0 0 !important;\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n}\n\n.job-title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 10px;\n}\n\n.job-desc {\n  margin-bottom: 10px;\n}\n\n.job-item {\n  display: flex;\n  margin-bottom: 5px;\n  font-size: 14px;\n  color: #666;\n}\n\n.job-salary {\n  text-align: right;\n}\n\n.salary-amount {\n  font-size: 18px;\n  font-weight: bold;\n  color: #f56c6c;\n}\n\n.salary-unit {\n  font-size: 14px;\n  color: #999;\n}\n</style>", "import mod from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=style&index=0&id=73456b22&scoped=true&lang=css&\"; export default mod; export * from \"-!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--6-oneOf-1-2!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-3!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../dev-tool/hhhhhbuilder/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./leidaCell.vue?vue&type=style&index=0&id=73456b22&scoped=true&lang=css&\""], "sourceRoot": ""}