(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/webview/webview"],{66:function(n,e,t){"use strict";(function(n,e){var r=t(4);t(26);r(t(25));var c=r(t(67));n.__webpack_require_UNI_MP_PLUGIN__=t,e(c.default)}).call(this,t(1)["default"],t(2)["createPage"])},67:function(n,e,t){"use strict";t.r(e);var r=t(68),c=t(70);for(var o in c)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return c[n]}))}(o);var i,u=t(32),a=Object(u["default"])(c["default"],r["render"],r["staticRenderFns"],!1,null,null,null,!1,r["components"],i);a.options.__file="bussinessPages/webview/webview.vue",e["default"]=a.exports},68:function(n,e,t){"use strict";t.r(e);var r=t(69);t.d(e,"render",(function(){return r["render"]})),t.d(e,"staticRenderFns",(function(){return r["staticRenderFns"]})),t.d(e,"recyclableRender",(function(){return r["recyclableRender"]})),t.d(e,"components",(function(){return r["components"]}))},69:function(n,e,t){"use strict";var r;t.r(e),t.d(e,"render",(function(){return c})),t.d(e,"staticRenderFns",(function(){return i})),t.d(e,"recyclableRender",(function(){return o})),t.d(e,"components",(function(){return r}));var c=function(){var n=this,e=n.$createElement;n._self._c},o=!1,i=[];c._withStripped=!0},70:function(n,e,t){"use strict";t.r(e);var r=t(71),c=t.n(r);for(var o in r)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(o);e["default"]=c.a},71:function(n,e,t){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r={data:function(){return{path:""}},onLoad:function(n){console.log("options",n),n.useCode?this.path="".concat(n.url,"?idx=").concat(n.idx,"&tit=").concat(n.tit):this.path=n.id?"".concat(null===n||void 0===n?void 0:n.url,"?id=").concat(n.id):"".concat(null===n||void 0===n?void 0:n.url,"?code=").concat(n.code)}};e.default=r}},[[66,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/bussinessPages/webview/webview.js.map