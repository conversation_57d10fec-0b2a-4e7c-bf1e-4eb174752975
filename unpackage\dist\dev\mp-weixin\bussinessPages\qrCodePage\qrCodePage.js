(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["bussinessPages/qrCodePage/qrCodePage"],{134:function(e,n,t){"use strict";(function(e,n){var o=t(4);t(26);o(t(25));var c=o(t(135));e.__webpack_require_UNI_MP_PLUGIN__=t,n(c.default)}).call(this,t(1)["default"],t(2)["createPage"])},135:function(e,n,t){"use strict";t.r(n);var o=t(136),c=t(138);for(var r in c)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return c[e]}))}(r);t(149);var i,s=t(32),a=Object(s["default"])(c["default"],o["render"],o["staticRenderFns"],!1,null,"c10c9c36",null,!1,o["components"],i);a.options.__file="bussinessPages/qrCodePage/qrCodePage.vue",n["default"]=a.exports},136:function(e,n,t){"use strict";t.r(n);var o=t(137);t.d(n,"render",(function(){return o["render"]})),t.d(n,"staticRenderFns",(function(){return o["staticRenderFns"]})),t.d(n,"recyclableRender",(function(){return o["recyclableRender"]})),t.d(n,"components",(function(){return o["components"]}))},137:function(e,n,t){"use strict";var o;t.r(n),t.d(n,"render",(function(){return c})),t.d(n,"staticRenderFns",(function(){return i})),t.d(n,"recyclableRender",(function(){return r})),t.d(n,"components",(function(){return o}));var c=function(){var e=this,n=e.$createElement;e._self._c},r=!1,i=[];c._withStripped=!0},138:function(e,n,t){"use strict";t.r(n);var o=t(139),c=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=c.a},139:function(e,n,t){"use strict";(function(e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o={data:function(){return{toggleQr:"",toggleTit:""}},methods:{handleLongPress:function(){var n=this;e.showActionSheet({itemList:["转发给朋友","保存到手机","收藏"],success:function(e){switch(e.tapIndex){case 0:n.shareToFriend();break;case 1:n.saveImageToPhone();break;case 2:n.collectImage();break}},fail:function(e){console.log("操作取消",e)}})},shareToFriend:function(){e.showToast({title:"请点击右上角分享",icon:"none"})},saveImageToPhone:function(){e.getImageInfo({src:this.toggleQr,success:function(n){e.saveImageToPhotosAlbum({filePath:n.path,success:function(){e.showToast({title:"保存成功",icon:"success"})},fail:function(n){-1!==n.errMsg.indexOf("auth deny")?e.showModal({title:"提示",content:"需要您授权保存相册",confirmText:"去设置",success:function(n){n.confirm&&e.openSetting()}}):e.showToast({title:"保存失败",icon:"none"})}})},fail:function(){e.showToast({title:"获取图片信息失败",icon:"none"})}})},collectImage:function(){e.showToast({title:"收藏成功",icon:"success"})}},onLoad:function(e){this.toggleTit=e.tit,this.toggleQr=t(140)("./".concat(e.idx,".jpg"))}};n.default=o}).call(this,t(2)["default"])},149:function(e,n,t){"use strict";t.r(n);var o=t(150),c=t.n(o);for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);n["default"]=c.a},150:function(e,n,t){}},[[134,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/bussinessPages/qrCodePage/qrCodePage.js.map