/* [z-paging]公用的静态css资源 */
.zp-line-loading-image.data-v-ef0d5cb6 {

	-webkit-animation: loading-flower-data-v-ef0d5cb6 1s steps(12) infinite;
	        animation: loading-flower-data-v-ef0d5cb6 1s steps(12) infinite;

	color: #666666;
}
.zp-line-loading-image-rpx.data-v-ef0d5cb6 {
	margin-right: 8rpx;
	width: 34rpx;
	height: 34rpx;
}
.zp-line-loading-image-px.data-v-ef0d5cb6 {
	margin-right: 4px;
	width: 17px;
	height: 17px;
}
.zp-loading-image-ios-rpx.data-v-ef0d5cb6 {
	width: 40rpx;
	height: 40rpx;
}
.zp-loading-image-ios-px.data-v-ef0d5cb6 {
	width: 20px;
	height: 20px;
}
.zp-loading-image-android-rpx.data-v-ef0d5cb6 {
	width: 34rpx;
	height: 34rpx;
}
.zp-loading-image-android-px.data-v-ef0d5cb6 {
	width: 17px;
	height: 17px;
}
@-webkit-keyframes loading-flower-data-v-ef0d5cb6 {
0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
}
to {
		-webkit-transform: rotate(1turn);
		transform: rotate(1turn);
}
}
@keyframes loading-flower-data-v-ef0d5cb6 {
0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
}
to {
		-webkit-transform: rotate(1turn);
		transform: rotate(1turn);
}
}
.zp-l-container.data-v-ef0d5cb6 {

	clear: both;
	display: flex;

	flex-direction: row;
	align-items: center;
	justify-content: center;
}
.zp-l-container-rpx.data-v-ef0d5cb6 {
	height: 80rpx;
	font-size: 27rpx;
}
.zp-l-container-px.data-v-ef0d5cb6 {
	height: 40px;
	font-size: 14px;
}
.zp-l-line-loading-custom-image.data-v-ef0d5cb6 {
	color: #a4a4a4;
}
.zp-l-line-loading-custom-image-rpx.data-v-ef0d5cb6 {
	margin-right: 8rpx;
	width: 28rpx;
	height: 28rpx;
}
.zp-l-line-loading-custom-image-px.data-v-ef0d5cb6 {
	margin-right: 4px;
	width: 14px;
	height: 14px;
}
.zp-l-line-loading-custom-image-animated.data-v-ef0d5cb6{

	-webkit-animation: loading-circle-data-v-ef0d5cb6 1s linear infinite;
	        animation: loading-circle-data-v-ef0d5cb6 1s linear infinite;
}
.zp-l-circle-loading-view.data-v-ef0d5cb6 {
	border: 3rpx solid #dddddd;
	border-radius: 50%;

	-webkit-animation: loading-circle-data-v-ef0d5cb6 1s linear infinite;
	        animation: loading-circle-data-v-ef0d5cb6 1s linear infinite;
}
.zp-l-circle-loading-view-rpx.data-v-ef0d5cb6 {
	margin-right: 8rpx;
	width: 23rpx;
	height: 23rpx;
}
.zp-l-circle-loading-view-px.data-v-ef0d5cb6 {
	margin-right: 4px;
	width: 12px;
	height: 12px;
}
.zp-l-text-rpx.data-v-ef0d5cb6 {
	font-size: 30rpx;
	margin: 0rpx 6rpx;
}
.zp-l-text-px.data-v-ef0d5cb6 {
	font-size: 15px;
	margin: 0px 3px;
}
.zp-l-line-rpx.data-v-ef0d5cb6 {
	height: 1px;
	width: 100rpx;
	margin: 0rpx 10rpx;
}
.zp-l-line-px.data-v-ef0d5cb6 {
	height: 1px;
	width: 50px;
	margin: 0rpx 5px;
}
@-webkit-keyframes loading-circle-data-v-ef0d5cb6 {
0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
}
}
@keyframes loading-circle-data-v-ef0d5cb6 {
0% {
		-webkit-transform: rotate(0deg);
		transform: rotate(0deg);
}
100% {
		-webkit-transform: rotate(360deg);
		transform: rotate(360deg);
}
}



