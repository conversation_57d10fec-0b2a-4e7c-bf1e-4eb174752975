<view class="uni-file-picker"><block wx:if="{{title}}"><view class="uni-file-picker__header"><text class="file-title">{{title}}</text><text class="file-count">{{$root.g0+"/"+limitLength}}</text></view></block><block wx:if="{{fileMediatype==='image'&&showType==='grid'}}"><upload-image vue-id="ddc11690-1" readonly="{{readonly}}" image-styles="{{imageStyles}}" files-list="{{filesList}}" limit="{{limitLength}}" disablePreview="{{disablePreview}}" delIcon="{{delIcon}}" data-event-opts="{{[['^uploadFiles',[['uploadFiles']]],['^choose',[['choose']]],['^delFile',[['delFile']]]]}}" bind:uploadFiles="__e" bind:choose="__e" bind:delFile="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><view class="is-add"><view class="icon-add"></view><view class="icon-add rotate"></view></view></block></upload-image></block><block wx:if="{{fileMediatype!=='image'||showType!=='grid'}}"><upload-file vue-id="ddc11690-2" readonly="{{readonly}}" list-styles="{{listStyles}}" files-list="{{filesList}}" showType="{{showType}}" delIcon="{{delIcon}}" data-event-opts="{{[['^uploadFiles',[['uploadFiles']]],['^choose',[['choose']]],['^delFile',[['delFile']]]]}}" bind:uploadFiles="__e" bind:choose="__e" bind:delFile="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><button class="file-btn" size="mini">选择文件</button></block></upload-file></block></view>