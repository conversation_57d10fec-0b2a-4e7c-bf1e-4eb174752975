(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map"],{204:function(t,e,a){"use strict";a.r(e);var n=a(205),i=a(207);for(var r in i)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return i[t]}))}(r);a(215);var o,s=a(32),u=Object(s["default"])(i["default"],n["render"],n["staticRenderFns"],!1,null,null,null,!1,n["components"],o);u.options.__file="uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.vue",e["default"]=u.exports},205:function(t,e,a){"use strict";a.r(e);var n=a(206);a.d(e,"render",(function(){return n["render"]})),a.d(e,"staticRenderFns",(function(){return n["staticRenderFns"]})),a.d(e,"recyclableRender",(function(){return n["recyclableRender"]})),a.d(e,"components",(function(){return n["components"]}))},206:function(t,e,a){"use strict";var n;a.r(e),a.d(e,"render",(function(){return i})),a.d(e,"staticRenderFns",(function(){return o})),a.d(e,"recyclableRender",(function(){return r})),a.d(e,"components",(function(){return n}));var i=function(){var t=this,e=t.$createElement;t._self._c},r=!1,o=[];i._withStripped=!0},207:function(t,e,a){"use strict";a.r(e);var n=a(208),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},208:function(t,e,a){"use strict";(function(t){var n=a(4);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(a(18)),r={props:{centerLat:{type:[String,Number],default:""},centerLng:{type:[String,Number],default:""},markerData:{type:Array,default:function(){return[]}},polygons:{type:Array,default:function(){return[]}},markerIconWidth:{type:Number,default:22},markerIconHeight:{type:Number,default:32},markerIconUrl:{type:String,default:""},scale:{type:Number,default:16},isShowCompass:{type:Boolean,default:!1},isEnableZoom:{type:Boolean,default:!0},isEnableScroll:{type:Boolean,default:!0},isEnableRotate:{type:Boolean,default:!1},goImgIn:"",markerImgIn:"",closeIcon:""},watch:{markerData:{immediate:!0,deep:!0,handler:function(t,e){this.markerDatas=t,this.showMarkers()}},markerImgIn:{handler:function(t){},immediate:!0,deep:!0},goImgIn:{handler:function(t){},immediate:!0,deep:!0},closeIcon:{handler:function(t){},immediate:!0,deep:!0},polygons:{immediate:!0,deep:!0,handler:function(t,e){this.polygonsData=(0,i.default)(t)}}},data:function(){return{markerImg:"../../static/marker.png",goImg:a(209),myaddressImg:a(210),wxmapImg:a(211),myaddressOnImg:a(212),wxmapOnImg:a(213),closeImg:a(214),polygonsData:[],markers:[],detailData:{},nowLat:"",nowLng:"",tabIndex:!1,tabIndex2:!1,isShowWxMap:!1,isShowDetail:!1,wxMapShow:!1}},mounted:function(){var e=t.getSystemInfoSync().uniPlatform;"mp-weixin"==e&&(this.wxMapShow=!0),this.showMarkers(),this.markerData||this.getLocation()},methods:{changeTab:function(t){1==t?(this.tabIndex=!this.tabIndex,this.tabIndex?this.getLocation():this.showMarkers()):(this.tabIndex2=!this.tabIndex2,this.tabIndex2?this.isShowWxMap=!0:this.isShowWxMap=!1)},getLocation:function(){var e=this;t.getLocation({type:"gcj02",isHighAccuracy:!0,highAccuracyExpireTime:3500,success:function(a){console.log("获取地址",a),e.nowLat=a.latitude,e.nowLng=a.longitude;var n=[{id:9999,latitude:a.latitude||"",longitude:a.longitude||"",width:e.markerIconWidth,height:e.markerIconHeight,iconPath:e.markerImg}];e.markers=[].concat(n);var i=t.createMapContext("esaymap",e);i.moveToLocation({latitude:a.latitude,longitude:a.longitude},{complete:function(t){}})},fail:function(e){"getLocation:fail auth deny"==e.errMsg&&t.showModal({content:"检测到您没打开获取信息功能权限，是否去设置打开？",confirmText:"确认",cancelText:"取消",success:function(e){if(!e.confirm)return!1;t.openSetting({success:function(t){}})}})}})},goRoute:function(){t.openLocation({latitude:+this.detailData.latitude,longitude:+this.detailData.longitude,scale:17,name:this.detailData.name||"--",address:this.detailData.address||"--"})},clearMarker:function(){this.markers=[]},showMarkers:function(){if(this.markerDatas&&this.markerDatas.length>0){for(var t=[],e=0;e<this.markerDatas.length;e++)t.push({id:Number(this.markerDatas[e].id),latitude:this.markerDatas[e].latitude||"",longitude:this.markerDatas[e].longitude||"",iconPath:this.markerDatas[e].markerUrl?this.markerDatas[e].markerUrl:this.markerImg,rotate:0,width:this.markerDatas[e].iconWidth?this.markerDatas[e].iconWidth:this.markerIconWidth,height:this.markerDatas[e].iconHeight?this.markerDatas[e].iconHeight:this.markerIconHeight,callout:{content:this.markerDatas[e].name,color:this.markerDatas[e].calloutColor||"#ffffff",fontSize:this.markerDatas[e].calloutFontSize||14,borderRadius:this.markerDatas[e].calloutBorderRadius||6,padding:this.markerDatas[e].calloutPadding||6,bgColor:this.markerDatas[e].calloutBgColor||"#0B6CFF",display:this.markerDatas[e].calloutDisplay||"BYCLICK"}});this.markers=t}},chooseItem:function(t){for(var e=t.detail.markerId,a=0;a<this.markerDatas.length;a++)if(this.markerDatas[a].id==e){this.isShowDetail=!0,this.detailData=this.markerDatas[a],this.$emit("clickMarker",this.markerDatas[a]);break}},clickMap:function(t){var e=t.detail.latitude.toFixed(5),a=t.detail.longitude.toFixed(5);this.$emit("clickMap",{latitude:e,longitude:a})},closeDetail:function(){this.detailData={},this.isShowDetail=!1}}};e.default=r}).call(this,a(2)["default"])},215:function(t,e,a){"use strict";a.r(e);var n=a(216),i=a.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){a.d(e,t,(function(){return n[t]}))}(r);e["default"]=i.a},216:function(t,e,a){}}]);
//# sourceMappingURL=../../../../../.sourcemap/mp-weixin/uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map.js.map
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map-create-component',
    {
        'uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('2')['createComponent'](__webpack_require__(204))
        })
    },
    [['uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map-create-component']]
]);
