<view class="page-container data-v-3deb4e8e"><view style="height:200rpx;" class="data-v-3deb4e8e"></view><z-paging vue-id="5c0c1641-1" auto="{{false}}" fixed="{{false}}" height="86vh" data-ref="paging" data-event-opts="{{[['^query',[['queryList']]],['^onRefresh',[['onRefresh']]]]}}" bind:query="__e" bind:onRefresh="__e" class="data-v-3deb4e8e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{$root.l0}}" wx:for-item="cell" wx:for-index="idx" wx:key="idx"><policy-cell-vue vue-id="{{('5c0c1641-2-'+idx)+','+('5c0c1641-1')}}" obj="{{cell.a0}}" data-event-opts="{{[['^onDetail',[['handlerOpenPriviewFile',['$0'],[[['dataList','',idx]]]]]]]}}" bind:onDetail="__e" class="data-v-3deb4e8e" bind:__l="__l"></policy-cell-vue></block></z-paging></view>