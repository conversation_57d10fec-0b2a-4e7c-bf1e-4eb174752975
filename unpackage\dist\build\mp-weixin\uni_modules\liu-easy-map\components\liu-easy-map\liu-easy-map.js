(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map"],{"0bea":function(t,a,e){},"2b89":function(t,a,e){"use strict";(function(t){var i=e("47a9");Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var n=i(e("af34")),r={props:{centerLat:{type:[String,Number],default:""},centerLng:{type:[String,Number],default:""},markerData:{type:Array,default:function(){return[]}},polygons:{type:Array,default:function(){return[]}},markerIconWidth:{type:Number,default:22},markerIconHeight:{type:Number,default:32},markerIconUrl:{type:String,default:""},scale:{type:Number,default:16},isShowCompass:{type:Boolean,default:!1},isEnableZoom:{type:Boolean,default:!0},isEnableScroll:{type:Boolean,default:!0},isEnableRotate:{type:Boolean,default:!1},goImgIn:"",markerImgIn:"",closeIcon:""},watch:{markerData:{immediate:!0,deep:!0,handler:function(t,a){this.markerDatas=t,this.showMarkers()}},markerImgIn:{handler:function(t){},immediate:!0,deep:!0},goImgIn:{handler:function(t){},immediate:!0,deep:!0},closeIcon:{handler:function(t){},immediate:!0,deep:!0},polygons:{immediate:!0,deep:!0,handler:function(t,a){this.polygonsData=(0,n.default)(t)}}},data:function(){return{markerImg:"../../static/marker.png",goImg:e("ee65"),myaddressImg:e("6518"),wxmapImg:e("ee4e"),myaddressOnImg:e("a309"),wxmapOnImg:e("79da"),closeImg:e("718c"),polygonsData:[],markers:[],detailData:{},nowLat:"",nowLng:"",tabIndex:!1,tabIndex2:!1,isShowWxMap:!1,isShowDetail:!1,wxMapShow:!1}},mounted:function(){var a=t.getSystemInfoSync().uniPlatform;"mp-weixin"==a&&(this.wxMapShow=!0),this.showMarkers(),this.markerData||this.getLocation()},methods:{changeTab:function(t){1==t?(this.tabIndex=!this.tabIndex,this.tabIndex?this.getLocation():this.showMarkers()):(this.tabIndex2=!this.tabIndex2,this.tabIndex2?this.isShowWxMap=!0:this.isShowWxMap=!1)},getLocation:function(){var a=this;t.getLocation({type:"gcj02",isHighAccuracy:!0,highAccuracyExpireTime:3500,success:function(e){console.log("获取地址",e),a.nowLat=e.latitude,a.nowLng=e.longitude;var i=[{id:9999,latitude:e.latitude||"",longitude:e.longitude||"",width:a.markerIconWidth,height:a.markerIconHeight,iconPath:a.markerImg}];a.markers=[].concat(i);var n=t.createMapContext("esaymap",a);n.moveToLocation({latitude:e.latitude,longitude:e.longitude},{complete:function(t){}})},fail:function(a){"getLocation:fail auth deny"==a.errMsg&&t.showModal({content:"检测到您没打开获取信息功能权限，是否去设置打开？",confirmText:"确认",cancelText:"取消",success:function(a){if(!a.confirm)return!1;t.openSetting({success:function(t){}})}})}})},goRoute:function(){t.openLocation({latitude:+this.detailData.latitude,longitude:+this.detailData.longitude,scale:17,name:this.detailData.name||"--",address:this.detailData.address||"--"})},clearMarker:function(){this.markers=[]},showMarkers:function(){if(this.markerDatas&&this.markerDatas.length>0){for(var t=[],a=0;a<this.markerDatas.length;a++)t.push({id:Number(this.markerDatas[a].id),latitude:this.markerDatas[a].latitude||"",longitude:this.markerDatas[a].longitude||"",iconPath:this.markerDatas[a].markerUrl?this.markerDatas[a].markerUrl:this.markerImg,rotate:0,width:this.markerDatas[a].iconWidth?this.markerDatas[a].iconWidth:this.markerIconWidth,height:this.markerDatas[a].iconHeight?this.markerDatas[a].iconHeight:this.markerIconHeight,callout:{content:this.markerDatas[a].name,color:this.markerDatas[a].calloutColor||"#ffffff",fontSize:this.markerDatas[a].calloutFontSize||14,borderRadius:this.markerDatas[a].calloutBorderRadius||6,padding:this.markerDatas[a].calloutPadding||6,bgColor:this.markerDatas[a].calloutBgColor||"#0B6CFF",display:this.markerDatas[a].calloutDisplay||"BYCLICK"}});this.markers=t}},chooseItem:function(t){for(var a=t.detail.markerId,e=0;e<this.markerDatas.length;e++)if(this.markerDatas[e].id==a){this.isShowDetail=!0,this.detailData=this.markerDatas[e],this.$emit("clickMarker",this.markerDatas[e]);break}},clickMap:function(t){var a=t.detail.latitude.toFixed(5),e=t.detail.longitude.toFixed(5);this.$emit("clickMap",{latitude:a,longitude:e})},closeDetail:function(){this.detailData={},this.isShowDetail=!1}}};a.default=r}).call(this,e("df3c")["default"])},"5c06":function(t,a,e){"use strict";var i=e("0bea"),n=e.n(i);n.a},6018:function(t,a,e){"use strict";e.d(a,"b",(function(){return i})),e.d(a,"c",(function(){return n})),e.d(a,"a",(function(){}));var i=function(){var t=this.$createElement;this._self._c},n=[]},"9d46":function(t,a,e){"use strict";e.r(a);var i=e("2b89"),n=e.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){e.d(a,t,(function(){return i[t]}))}(r);a["default"]=n.a},e2ea:function(t,a,e){"use strict";e.r(a);var i=e("6018"),n=e("9d46");for(var r in n)["default"].indexOf(r)<0&&function(t){e.d(a,t,(function(){return n[t]}))}(r);e("5c06");var o=e("828b"),s=Object(o["a"])(n["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);a["default"]=s.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map-create-component',
    {
        'uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("e2ea"))
        })
    },
    [['uni_modules/liu-easy-map/components/liu-easy-map/liu-easy-map-create-component']]
]);
