<view class="uni-data-tree"><view data-event-opts="{{[['tap',[['handleInput',['$event']]]]]}}" class="uni-data-tree-input" bindtap="__e"><block wx:if="{{$slots.default}}"><slot></slot><scoped-slots-default options="{{options}}" data="{{inputSelected}}" error="{{errorMessage}}" class="scoped-ref" bind:__l="__l"></scoped-slots-default></block><block wx:else><view class="{{['input-value',(border)?'input-value-border':'']}}"><block wx:if="{{errorMessage}}"><text class="selected-area error-text">{{errorMessage}}</text></block><block wx:else><block wx:if="{{loading&&!isOpened}}"><view class="selected-area"><uni-load-more class="load-more" vue-id="c91765b4-1" contentText="{{loadMore}}" status="loading" bind:__l="__l"></uni-load-more></view></block><block wx:else><block wx:if="{{$root.g0}}"><scroll-view class="selected-area" scroll-x="true"><view class="selected-list"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="selected-item"><text class="text-color">{{item.$orig.text}}</text><block wx:if="{{index<item.g1-1}}"><text class="input-split-line">{{split}}</text></block></view></block></view></scroll-view></block><block wx:else><text class="selected-area placeholder">{{placeholder}}</text></block></block></block><block wx:if="{{$root.g2}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="icon-clear" catchtap="__e"><uni-icons vue-id="c91765b4-2" type="clear" color="#c0c4cc" size="24" bind:__l="__l"></uni-icons></view></block><block wx:if="{{$root.g3}}"><view class="arrow-area"><view class="input-arrow"></view></view></block></view></block></view><block wx:if="{{isOpened}}"><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="uni-data-tree-cover" bindtap="__e"></view></block><block wx:if="{{isOpened}}"><view class="uni-data-tree-dialog"><view class="uni-popper__arrow"></view><view class="dialog-caption"><view class="title-area"><text class="dialog-title">{{popupTitle}}</text></view><view data-event-opts="{{[['tap',[['handleClose',['$event']]]]]}}" class="dialog-close" bindtap="__e"><view class="dialog-close-plus" data-id="close"></view><view class="dialog-close-plus dialog-close-rotate" data-id="close"></view></view></view><data-picker-view class="picker-view vue-ref" vue-id="c91765b4-3" localdata="{{localdata}}" preload="{{preload}}" collection="{{collection}}" field="{{field}}" orderby="{{orderby}}" where="{{where}}" step-searh="{{stepSearh}}" self-field="{{selfField}}" parent-field="{{parentField}}" managed-mode="{{true}}" map="{{map}}" ellipsis="{{ellipsis}}" data-ref="pickerView" value="{{dataValue}}" data-event-opts="{{[['^change',[['onchange']]],['^datachange',[['ondatachange']]],['^nodeclick',[['onnodeclick']]],['^input',[['__set_model',['','dataValue','$event',[]]]]]]}}" bind:change="__e" bind:datachange="__e" bind:nodeclick="__e" bind:input="__e" bind:__l="__l"></data-picker-view></view></block></view>